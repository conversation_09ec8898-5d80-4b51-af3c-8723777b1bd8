// Package imports:
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../config/defines/keys.dart';
import '../core/network/ez_network.dart';
import '../core/routes/app_router.dart';
import '../data/datasources/ez_datasources.dart';
import '../data/datasources/remote/api_services.dart';
import '../data/datasources/remote/chat_api_service.dart';
import '../data/datasources/remote/comment_list_api_service.dart';
import '../data/datasources/remote/create_chat_group_api_service.dart';
import '../data/datasources/remote/like_list_api_service.dart';
import '../data/datasources/remote/media_upload_api_service.dart';
import '../data/datasources/remote/note_list_api_service.dart';
import '../data/datasources/remote/rating_human_api_service.dart';
import '../data/datasources/remote/social_upload_api_service.dart';
import '../data/datasources/remote/sticker_social_api_service.dart';
import '../data/datasources/remote/story_list_api_service.dart';
import '../data/datasources/remote/story_person_list_api_service.dart';
import '../data/datasources/remote/tag_image_service.dart';
import '../data/datasources/remote/ticket_active_api_service.dart';
import '../data/datasources/remote/ticket_api_service.dart';
import '../injector/injector.dart';

@module
abstract class RegisterModule {
  // register named for collaborator api services
  // register named for api services
  @Named(kApiDio)
  Dio get collaboratorApiDio =>
      getIt<ApiNetwork>().collaboratorApiProvider.apiDio;
  @Named(kApiBaseUrl)
  String get collaboratorApiBaseUrl =>
      getIt<ApiNetwork>().collaboratorApiProvider.apiDio.options.baseUrl;

  // register named for static api Social
  @Named(kApiSocialDio)
  Dio get collaboratorApiSocialDio =>
      getIt<ApiNetwork>().apiSocialProvider.apiDio;
  @Named(kApiSocialBaseUrl)
  String get collaboratorApiSocialBaseUrl =>
      getIt<ApiNetwork>().apiSocialProvider.apiDio.options.baseUrl;

  // register named for static api services
  @Named(kStaticApiDio)
  Dio get collaboratorStaticApiDio =>
      getIt<ApiNetwork>().collaboratorUploadProvider.imageDio;
  @Named(kStaticApiBaseUrl)
  String get collaboratorStaticApiBaseUrl =>
      getIt<ApiNetwork>().collaboratorUploadProvider.imageDio.options.baseUrl;

  @Named(kStaticCustomerApiDio)
  Dio get customerStaticApiDio =>
      getIt<ApiNetwork>().customerUploadProvider.apiDio;
  @Named(kStaticCustomerApiBaseUrl)
  String get customerStaticApiBaseUrl =>
      getIt<ApiNetwork>().customerUploadProvider.apiDio.options.baseUrl;

  @Named(kApiGoogleMapDio)
  Dio get googleMapApiDio => getIt<ApiNetwork>().apiGoogleMapProvider.apiDio;
  @Named(kApiGoogleMapBaseUrl)
  String get googleMapApiBaseUrl =>
      getIt<ApiNetwork>().apiGoogleMapProvider.apiDio.options.baseUrl;

  @Named(kApiAiDio)
  Dio get aIApiDio => getIt<ApiNetwork>().apiAiProvider.apiDio;
  @Named(kApiAiBaseUrl)
  String get aIApiBaseUrl =>
      getIt<ApiNetwork>().apiAiProvider.apiDio.options.baseUrl;

  // ================== Image Cache Manager ========================
  @lazySingleton
  DefaultCacheManager get cacheManager => DefaultCacheManager();

  // ================== Collaborator module ========================
  @lazySingleton
  EZCache get collaboratorCache => EZCache.shared;

  @lazySingleton
  AppRouter get appRouter => AppRouter();

  @lazySingleton
  EformApiService eformApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => EformApiService(dio, baseUrl: url);

  @lazySingleton
  UserApiService collaboratorUserApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => UserApiService(dio, baseUrl: url);

  @lazySingleton
  StaffApiService staffApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => StaffApiService(dio, baseUrl: url);

  @lazySingleton
  HomeApiService collaboratorHomeApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => HomeApiService(dio, baseUrl: url);

  @lazySingleton
  CustomerApiService collaboratorCustomerApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CustomerApiService(dio, baseUrl: url);

  @lazySingleton
  CheckinApiService checkinApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CheckinApiService(dio, baseUrl: url);

  @lazySingleton
  NewsApiService collaboratornewsApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => NewsApiService(dio, baseUrl: url);

  @lazySingleton
  NotificationApiService collaboratorNotificationApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => NotificationApiService(dio, baseUrl: url);

  @lazySingleton
  TaskApiService taskApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => TaskApiService(dio, baseUrl: url);

  @lazySingleton
  StaticApiService collaboratorStaticApiService(
    @Named(kStaticApiDio) final Dio dio,
    @Named(kStaticApiBaseUrl) final String url,
  ) => StaticApiService(dio, baseUrl: url);

  @lazySingleton
  RequestApiService collaboratorRequestApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => RequestApiService(dio, baseUrl: url);

  @lazySingleton
  TrackingApiService trackingApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => TrackingApiService(dio, baseUrl: url);

  @lazySingleton
  BranchSelectionApiService branchSelectionApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => BranchSelectionApiService(dio, baseUrl: url);

  @lazySingleton
  ImportantNotesApiService importantNotesApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => ImportantNotesApiService(dio, baseUrl: url);

  @lazySingleton
  CustomerProfileApiService customerProfileApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CustomerProfileApiService(dio, baseUrl: url);

  @lazySingleton
  CustomerInfoDetailsApiService customerInfoDetailsApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CustomerInfoDetailsApiService(dio, baseUrl: url);

  @lazySingleton
  NoteDetailsApiService noteDetailsApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => NoteDetailsApiService(dio, baseUrl: url);

  @lazySingleton
  MedicalDepartmentListApiService medicalDepartmentListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => MedicalDepartmentListApiService(dio, baseUrl: url);

  @lazySingleton
  ListCustomerApiService listCustomerApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => ListCustomerApiService(dio, baseUrl: url);

  @lazySingleton
  ServiceAndProductApiService serviceAndProductApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => ServiceAndProductApiService(dio, baseUrl: url);

  @lazySingleton
  MedicalServiceLogListApiService medicalServiceLogListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => MedicalServiceLogListApiService(dio, baseUrl: url);

  @lazySingleton
  MedicalLogDetailApiService medicalLogDetailApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => MedicalLogDetailApiService(dio, baseUrl: url);

  @lazySingleton
  MedicineDetailApiService medicineDetailApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => MedicineDetailApiService(dio, baseUrl: url);

  MedicalServiceListApiService medicalServiceListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => MedicalServiceListApiService(dio, baseUrl: url);

  @lazySingleton
  MedicalTemplateListApiService medicalTemplateListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => MedicalTemplateListApiService(dio, baseUrl: url);

  @lazySingleton
  MedicalProductCreationApiService medicalProductCreationApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => MedicalProductCreationApiService(dio, baseUrl: url);

  @lazySingleton
  MedicalServiceCreationApiService medicalServiceCreationApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => MedicalServiceCreationApiService(dio, baseUrl: url);

  @lazySingleton
  FoodApiService foodApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => FoodApiService(dio, baseUrl: url);

  @lazySingleton
  ScheduleDetailsApiService scheduleDetailsApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => ScheduleDetailsApiService(dio, baseUrl: url);

  @lazySingleton
  CustomerScheduleApiService customerScheduleApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CustomerScheduleApiService(dio, baseUrl: url);

  @lazySingleton
  CustomerBookingInfoApiService customerBookingInfoApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CustomerBookingInfoApiService(dio, baseUrl: url);

  @lazySingleton
  AssignTaskApiService assignTaskApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => AssignTaskApiService(dio, baseUrl: url);

  @lazySingleton
  ChatSelectBranchApiService chatSelectBranchApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => ChatSelectBranchApiService(dio, baseUrl: url);

  @lazySingleton
  BranchChatListApiService branchChatListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => BranchChatListApiService(dio, baseUrl: url);

  @lazySingleton
  PxListApiService pxListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => PxListApiService(dio, baseUrl: url);

  @lazySingleton
  PxUnasignedApiService pxUnasignedApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => PxUnasignedApiService(dio, baseUrl: url);

  @lazySingleton
  PxTaskListApiService pxTaskListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => PxTaskListApiService(dio, baseUrl: url);

  @lazySingleton
  PxUnasignedUpdateApiService pxUnasignedUpdateApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => PxUnasignedUpdateApiService(dio, baseUrl: url);

  @lazySingleton
  TakingCareCustomerApiService takingCareCustomerApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => TakingCareCustomerApiService(dio, baseUrl: url);

  @lazySingleton
  PxRecheckApiService pxRecheckApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => PxRecheckApiService(dio, baseUrl: url);

  @lazySingleton
  MediaUploadApiService recordApiService(
    @Named(kStaticCustomerApiDio) final Dio dio,
    @Named(kStaticCustomerApiBaseUrl) final String url,
  ) => MediaUploadApiService(dio, baseUrl: url);

  @lazySingleton
  CreateCustomerApiService createCustomerApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CreateCustomerApiService(dio, baseUrl: url);

  @lazySingleton
  SelectPxRoomApiService selectPxRoomApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => SelectPxRoomApiService(dio, baseUrl: url);

  @lazySingleton
  CustomerListApiService customerListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CustomerListApiService(dio, baseUrl: url);

  @lazySingleton
  ConsultationManagerApiService consultationManagerApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => ConsultationManagerApiService(dio, baseUrl: url);

  @lazySingleton
  ConsultationCustomerApiService consultationCustomerApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => ConsultationCustomerApiService(dio, baseUrl: url);

  @lazySingleton
  StaffEvaluationPeriodsApiService staffEvaluationPeriodsApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => StaffEvaluationPeriodsApiService(dio, baseUrl: url);

  @lazySingleton
  DetailStaffEvaluationPeriodApiService detailStaffEvaluationPeriodApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => DetailStaffEvaluationPeriodApiService(dio, baseUrl: url);

  @lazySingleton
  RatingHumanApiService ratingHumanApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => RatingHumanApiService(dio, baseUrl: url);

  @lazySingleton
  NoteListApiService noteListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => NoteListApiService(dio, baseUrl: url);

  @lazySingleton
  DetailCrmCustomerApiService detailCrmCustomerApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => DetailCrmCustomerApiService(dio, baseUrl: url);

  @lazySingleton
  HrOrganizationApiService hrOrganizationApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => HrOrganizationApiService(dio, baseUrl: url);

  @lazySingleton
  CustomerRecordApiService customerRecordApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CustomerRecordApiService(dio, baseUrl: url);

  @lazySingleton
  CheckinPhotoApiService checkinPhotoApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CheckinPhotoApiService(dio, baseUrl: url);

  @lazySingleton
  FeedbackApiService feedbackApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => FeedbackApiService(dio, baseUrl: url);

  @lazySingleton
  ChatApiService chatApiService(
    @Named(kStaticCustomerApiDio) final Dio dio,
    @Named(kStaticCustomerApiBaseUrl) final String url,
  ) => ChatApiService(dio, baseUrl: url);
  @lazySingleton
  SocialApiService socialApiService(
    @Named(kStaticCustomerApiDio) final Dio dio,
    @Named(kStaticCustomerApiBaseUrl) final String url,
  ) => SocialApiService(dio, baseUrl: url);
  @lazySingleton
  CreateChatGroupApiService createChatGroupApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CreateChatGroupApiService(dio, baseUrl: url);
  @lazySingleton
  StoryListApiService storyListApiService(
    @Named(kApiSocialDio) final Dio dio,
    @Named(kApiSocialBaseUrl) final String url,
  ) => StoryListApiService(dio, baseUrl: url);
  @lazySingleton
  CommentListApiService commentListApiService(
    @Named(kApiSocialDio) final Dio dio,
    @Named(kApiSocialBaseUrl) final String url,
  ) => CommentListApiService(dio, baseUrl: url);

  @lazySingleton
  LikeListApiService likeListApiService(
    @Named(kApiSocialDio) final Dio dio,
    @Named(kApiSocialBaseUrl) final String url,
  ) => LikeListApiService(dio, baseUrl: url);

  @lazySingleton
  StoryPersonListApiService storyPersonListApiService(
    @Named(kApiSocialDio) final Dio dio,
    @Named(kApiSocialBaseUrl) final String url,
  ) => StoryPersonListApiService(dio, baseUrl: url);

  @lazySingleton
  GroupChatDetailApiService groupChatDetailApiService(
    @Named(kStaticCustomerApiDio) final Dio dio,
    @Named(kStaticCustomerApiBaseUrl) final String url,
  ) => GroupChatDetailApiService(dio, baseUrl: url);

  @lazySingleton
  UserListApiService userListApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => UserListApiService(dio, baseUrl: url);
  @lazySingleton
  LocationGoogleApiService locationGoogleApiService(
    @Named(kApiGoogleMapDio) final Dio dio,
    @Named(kApiGoogleMapBaseUrl) final String url,
  ) => LocationGoogleApiService(dio, baseUrl: url);
  @lazySingleton
  TagListApiService tagListApiService(
    @Named(kApiSocialDio) final Dio dio,
    @Named(kApiSocialBaseUrl) final String url,
  ) => TagListApiService(dio, baseUrl: url);
  @lazySingleton
  StoryDetailApiService storyDetailApiService(
    @Named(kApiSocialDio) final Dio dio,
    @Named(kApiSocialBaseUrl) final String url,
  ) => StoryDetailApiService(dio, baseUrl: url);
  @lazySingleton
  NotificationListApiService notificationListApiService(
    @Named(kApiSocialDio) final Dio dio,
    @Named(kApiSocialBaseUrl) final String url,
  ) => NotificationListApiService(dio, baseUrl: url);
  @lazySingleton
  CreateChatFolderApiService createChatFolderApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => CreateChatFolderApiService(dio, baseUrl: url);
  @lazySingleton
  TicketApiService ticketApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => TicketApiService(dio, baseUrl: url);
  @lazySingleton
  TicketDetailApiService ticketDetailApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => TicketDetailApiService(dio, baseUrl: url);
  @lazySingleton
  TicketActiveApiService ticketActiveApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => TicketActiveApiService(dio, baseUrl: url);

  @lazySingleton
  DevApiService devApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => DevApiService(dio, baseUrl: url);

  @lazySingleton
  KpiEmployeeApiService kpiEmployeeApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => KpiEmployeeApiService(dio, baseUrl: url);

  @lazySingleton
  StickerSocialApiService stickerSocialApiService(
    @Named(kStaticCustomerApiDio) final Dio dio,
    @Named(kStaticCustomerApiBaseUrl) final String url,
  ) => StickerSocialApiService(dio, baseUrl: url);

  @lazySingleton
  ProductConfirmApiService productConfirmApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => ProductConfirmApiService(dio, baseUrl: url);

  @lazySingleton
  TagImageApiService tagImageApiService(
    @Named(kApiDio) final Dio dio,
    @Named(kApiBaseUrl) final String url,
  ) => TagImageApiService(dio, baseUrl: url);
}
