// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [AccHomePage]
class AccHomeRoute extends PageRouteInfo<AccHomeRouteArgs> {
  AccHomeRoute({
    Key? key,
    required AccLoginResponseModel? loginModel,
    List<PageRouteInfo>? children,
  }) : super(
          AccHomeRoute.name,
          args: AccHomeRouteArgs(
            key: key,
            loginModel: loginModel,
          ),
          initialChildren: children,
        );

  static const String name = 'AccHomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AccHomeRouteArgs>();
      return AccHomePage(
        key: args.key,
        loginModel: args.loginModel,
      );
    },
  );
}

class AccHomeRouteArgs {
  const AccHomeRouteArgs({
    this.key,
    required this.loginModel,
  });

  final Key? key;

  final AccLoginResponseModel? loginModel;

  @override
  String toString() {
    return 'AccHomeRouteArgs{key: $key, loginModel: $loginModel}';
  }
}

/// generated route for
/// [AccLandingPage]
class AccLandingRoute extends PageRouteInfo<void> {
  const AccLandingRoute({List<PageRouteInfo>? children})
      : super(
          AccLandingRoute.name,
          initialChildren: children,
        );

  static const String name = 'AccLandingRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const AccLandingPage();
    },
  );
}

/// generated route for
/// [AccLoginPage]
class AccLoginRoute extends PageRouteInfo<AccLoginRouteArgs> {
  AccLoginRoute({
    Key? key,
    bool isShowButtonClose = false,
    List<PageRouteInfo>? children,
  }) : super(
          AccLoginRoute.name,
          args: AccLoginRouteArgs(
            key: key,
            isShowButtonClose: isShowButtonClose,
          ),
          initialChildren: children,
        );

  static const String name = 'AccLoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AccLoginRouteArgs>(
          orElse: () => const AccLoginRouteArgs());
      return AccLoginPage(
        key: args.key,
        isShowButtonClose: args.isShowButtonClose,
      );
    },
  );
}

class AccLoginRouteArgs {
  const AccLoginRouteArgs({
    this.key,
    this.isShowButtonClose = false,
  });

  final Key? key;

  final bool isShowButtonClose;

  @override
  String toString() {
    return 'AccLoginRouteArgs{key: $key, isShowButtonClose: $isShowButtonClose}';
  }
}

/// generated route for
/// [AccountAssetPage]
class AccountAssetRoute extends PageRouteInfo<AccountAssetRouteArgs> {
  AccountAssetRoute({
    Key? key,
    ProfileAssetModel? asset,
    List<PageRouteInfo>? children,
  }) : super(
          AccountAssetRoute.name,
          args: AccountAssetRouteArgs(
            key: key,
            asset: asset,
          ),
          initialChildren: children,
        );

  static const String name = 'AccountAssetRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AccountAssetRouteArgs>(
          orElse: () => const AccountAssetRouteArgs());
      return AccountAssetPage(
        key: args.key,
        asset: args.asset,
      );
    },
  );
}

class AccountAssetRouteArgs {
  const AccountAssetRouteArgs({
    this.key,
    this.asset,
  });

  final Key? key;

  final ProfileAssetModel? asset;

  @override
  String toString() {
    return 'AccountAssetRouteArgs{key: $key, asset: $asset}';
  }
}

/// generated route for
/// [AccountPreviewPage]
class AccountPreviewRoute extends PageRouteInfo<AccountPreviewRouteArgs> {
  AccountPreviewRoute({
    Key? key,
    ProfileBundleModel? asset,
    List<PageRouteInfo>? children,
  }) : super(
          AccountPreviewRoute.name,
          args: AccountPreviewRouteArgs(
            key: key,
            asset: asset,
          ),
          initialChildren: children,
        );

  static const String name = 'AccountPreviewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AccountPreviewRouteArgs>(
          orElse: () => const AccountPreviewRouteArgs());
      return AccountPreviewPage(
        key: args.key,
        asset: args.asset,
      );
    },
  );
}

class AccountPreviewRouteArgs {
  const AccountPreviewRouteArgs({
    this.key,
    this.asset,
  });

  final Key? key;

  final ProfileBundleModel? asset;

  @override
  String toString() {
    return 'AccountPreviewRouteArgs{key: $key, asset: $asset}';
  }
}

/// generated route for
/// [ActionAttendancePage]
class ActionAttendanceRoute extends PageRouteInfo<ActionAttendanceRouteArgs> {
  ActionAttendanceRoute({
    Key? key,
    required String currentDate,
    List<PageRouteInfo>? children,
  }) : super(
          ActionAttendanceRoute.name,
          args: ActionAttendanceRouteArgs(
            key: key,
            currentDate: currentDate,
          ),
          initialChildren: children,
        );

  static const String name = 'ActionAttendanceRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ActionAttendanceRouteArgs>();
      return ActionAttendancePage(
        key: args.key,
        currentDate: args.currentDate,
      );
    },
  );
}

class ActionAttendanceRouteArgs {
  const ActionAttendanceRouteArgs({
    this.key,
    required this.currentDate,
  });

  final Key? key;

  final String currentDate;

  @override
  String toString() {
    return 'ActionAttendanceRouteArgs{key: $key, currentDate: $currentDate}';
  }
}

/// generated route for
/// [ApprovalOtpScreen]
class ApprovalOtpRoute extends PageRouteInfo<ApprovalOtpRouteArgs> {
  ApprovalOtpRoute({
    Key? key,
    required ApprovalOtpScreenBundle approvalOtpScreenBundle,
    List<PageRouteInfo>? children,
  }) : super(
          ApprovalOtpRoute.name,
          args: ApprovalOtpRouteArgs(
            key: key,
            approvalOtpScreenBundle: approvalOtpScreenBundle,
          ),
          initialChildren: children,
        );

  static const String name = 'ApprovalOtpRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ApprovalOtpRouteArgs>();
      return ApprovalOtpScreen(
        key: args.key,
        approvalOtpScreenBundle: args.approvalOtpScreenBundle,
      );
    },
  );
}

class ApprovalOtpRouteArgs {
  const ApprovalOtpRouteArgs({
    this.key,
    required this.approvalOtpScreenBundle,
  });

  final Key? key;

  final ApprovalOtpScreenBundle approvalOtpScreenBundle;

  @override
  String toString() {
    return 'ApprovalOtpRouteArgs{key: $key, approvalOtpScreenBundle: $approvalOtpScreenBundle}';
  }
}

/// generated route for
/// [AssetHistoryScreen]
class AssetHistoryRoute extends PageRouteInfo<AssetHistoryRouteArgs> {
  AssetHistoryRoute({
    Key? key,
    required String code,
    List<PageRouteInfo>? children,
  }) : super(
          AssetHistoryRoute.name,
          args: AssetHistoryRouteArgs(
            key: key,
            code: code,
          ),
          rawPathParams: {'id': code},
          initialChildren: children,
        );

  static const String name = 'AssetHistoryRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<AssetHistoryRouteArgs>(
          orElse: () =>
              AssetHistoryRouteArgs(code: pathParams.getString('id')));
      return AssetHistoryScreen(
        key: args.key,
        code: args.code,
      );
    },
  );
}

class AssetHistoryRouteArgs {
  const AssetHistoryRouteArgs({
    this.key,
    required this.code,
  });

  final Key? key;

  final String code;

  @override
  String toString() {
    return 'AssetHistoryRouteArgs{key: $key, code: $code}';
  }
}

/// generated route for
/// [AssignTaskPage]
class AssignTaskRoute extends PageRouteInfo<void> {
  const AssignTaskRoute({List<PageRouteInfo>? children})
      : super(
          AssignTaskRoute.name,
          initialChildren: children,
        );

  static const String name = 'AssignTaskRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const AssignTaskPage();
    },
  );
}

/// generated route for
/// [BaseWebviewScreen]
class BaseWebviewRoute extends PageRouteInfo<BaseWebviewRouteArgs> {
  BaseWebviewRoute({
    Key? key,
    required AccountMenusModel? accountMenus,
    List<PageRouteInfo>? children,
  }) : super(
          BaseWebviewRoute.name,
          args: BaseWebviewRouteArgs(
            key: key,
            accountMenus: accountMenus,
          ),
          initialChildren: children,
        );

  static const String name = 'BaseWebviewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<BaseWebviewRouteArgs>();
      return BaseWebviewScreen(
        key: args.key,
        accountMenus: args.accountMenus,
      );
    },
  );
}

class BaseWebviewRouteArgs {
  const BaseWebviewRouteArgs({
    this.key,
    required this.accountMenus,
  });

  final Key? key;

  final AccountMenusModel? accountMenus;

  @override
  String toString() {
    return 'BaseWebviewRouteArgs{key: $key, accountMenus: $accountMenus}';
  }
}

/// generated route for
/// [BedSelectionPage]
class BedSelectionRoute extends PageRouteInfo<BedSelectionRouteArgs> {
  BedSelectionRoute({
    Key? key,
    required BedSelectionPageParams params,
    List<PageRouteInfo>? children,
  }) : super(
          BedSelectionRoute.name,
          args: BedSelectionRouteArgs(
            key: key,
            params: params,
          ),
          initialChildren: children,
        );

  static const String name = 'BedSelectionRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<BedSelectionRouteArgs>();
      return BedSelectionPage(
        key: args.key,
        params: args.params,
      );
    },
  );
}

class BedSelectionRouteArgs {
  const BedSelectionRouteArgs({
    this.key,
    required this.params,
  });

  final Key? key;

  final BedSelectionPageParams params;

  @override
  String toString() {
    return 'BedSelectionRouteArgs{key: $key, params: $params}';
  }
}

/// generated route for
/// [BiometricsScreen]
class BiometricsRoute extends PageRouteInfo<BiometricsRouteArgs> {
  BiometricsRoute({
    Key? key,
    required BiometricType type,
    List<PageRouteInfo>? children,
  }) : super(
          BiometricsRoute.name,
          args: BiometricsRouteArgs(
            key: key,
            type: type,
          ),
          initialChildren: children,
        );

  static const String name = 'BiometricsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<BiometricsRouteArgs>();
      return BiometricsScreen(
        key: args.key,
        type: args.type,
      );
    },
  );
}

class BiometricsRouteArgs {
  const BiometricsRouteArgs({
    this.key,
    required this.type,
  });

  final Key? key;

  final BiometricType type;

  @override
  String toString() {
    return 'BiometricsRouteArgs{key: $key, type: $type}';
  }
}

/// generated route for
/// [BranchChatListPage]
class BranchChatListRoute extends PageRouteInfo<BranchChatListRouteArgs> {
  BranchChatListRoute({
    required String? branchId,
    required String? branchName,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          BranchChatListRoute.name,
          args: BranchChatListRouteArgs(
            branchId: branchId,
            branchName: branchName,
            key: key,
          ),
          rawPathParams: {
            'branchId': branchId,
            'branchName': branchName,
          },
          initialChildren: children,
        );

  static const String name = 'BranchChatListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<BranchChatListRouteArgs>(
          orElse: () => BranchChatListRouteArgs(
                branchId: pathParams.optString('branchId'),
                branchName: pathParams.optString('branchName'),
              ));
      return BranchChatListPage(
        branchId: args.branchId,
        branchName: args.branchName,
        key: args.key,
      );
    },
  );
}

class BranchChatListRouteArgs {
  const BranchChatListRouteArgs({
    required this.branchId,
    required this.branchName,
    this.key,
  });

  final String? branchId;

  final String? branchName;

  final Key? key;

  @override
  String toString() {
    return 'BranchChatListRouteArgs{branchId: $branchId, branchName: $branchName, key: $key}';
  }
}

/// generated route for
/// [BranchSelectionPage]
class BranchSelectionRoute extends PageRouteInfo<void> {
  const BranchSelectionRoute({List<PageRouteInfo>? children})
      : super(
          BranchSelectionRoute.name,
          initialChildren: children,
        );

  static const String name = 'BranchSelectionRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const BranchSelectionPage();
    },
  );
}

/// generated route for
/// [CallingPage]
class CallingRoute extends PageRouteInfo<CallingRouteArgs> {
  CallingRoute({
    required StringeeClient client,
    required String toUserId,
    required bool isIncomingCall,
    required String fromPhone,
    bool isVideoCall = false,
    bool acceptCallTapped = false,
    Key? key,
    StringeeCall? stringeeCall,
    bool isFullScreen = true,
    ValueNotifier<bool>? isCalling,
    List<PageRouteInfo>? children,
  }) : super(
          CallingRoute.name,
          args: CallingRouteArgs(
            client: client,
            toUserId: toUserId,
            isIncomingCall: isIncomingCall,
            fromPhone: fromPhone,
            isVideoCall: isVideoCall,
            acceptCallTapped: acceptCallTapped,
            key: key,
            stringeeCall: stringeeCall,
            isFullScreen: isFullScreen,
            isCalling: isCalling,
          ),
          initialChildren: children,
        );

  static const String name = 'CallingRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CallingRouteArgs>();
      return CallingPage(
        client: args.client,
        toUserId: args.toUserId,
        isIncomingCall: args.isIncomingCall,
        fromPhone: args.fromPhone,
        isVideoCall: args.isVideoCall,
        acceptCallTapped: args.acceptCallTapped,
        key: args.key,
        stringeeCall: args.stringeeCall,
        isFullScreen: args.isFullScreen,
        isCalling: args.isCalling,
      );
    },
  );
}

class CallingRouteArgs {
  const CallingRouteArgs({
    required this.client,
    required this.toUserId,
    required this.isIncomingCall,
    required this.fromPhone,
    this.isVideoCall = false,
    this.acceptCallTapped = false,
    this.key,
    this.stringeeCall,
    this.isFullScreen = true,
    this.isCalling,
  });

  final StringeeClient client;

  final String toUserId;

  final bool isIncomingCall;

  final String fromPhone;

  final bool isVideoCall;

  final bool acceptCallTapped;

  final Key? key;

  final StringeeCall? stringeeCall;

  final bool isFullScreen;

  final ValueNotifier<bool>? isCalling;

  @override
  String toString() {
    return 'CallingRouteArgs{client: $client, toUserId: $toUserId, isIncomingCall: $isIncomingCall, fromPhone: $fromPhone, isVideoCall: $isVideoCall, acceptCallTapped: $acceptCallTapped, key: $key, stringeeCall: $stringeeCall, isFullScreen: $isFullScreen, isCalling: $isCalling}';
  }
}

/// generated route for
/// [CameraPreviewPage]
class CameraPreviewRoute extends PageRouteInfo<CameraPreviewRouteArgs> {
  CameraPreviewRoute({
    Key? key,
    String? path,
    List<PageRouteInfo>? children,
  }) : super(
          CameraPreviewRoute.name,
          args: CameraPreviewRouteArgs(
            key: key,
            path: path,
          ),
          initialChildren: children,
        );

  static const String name = 'CameraPreviewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CameraPreviewRouteArgs>(
          orElse: () => const CameraPreviewRouteArgs());
      return CameraPreviewPage(
        key: args.key,
        path: args.path,
      );
    },
  );
}

class CameraPreviewRouteArgs {
  const CameraPreviewRouteArgs({
    this.key,
    this.path,
  });

  final Key? key;

  final String? path;

  @override
  String toString() {
    return 'CameraPreviewRouteArgs{key: $key, path: $path}';
  }
}

/// generated route for
/// [ChatListPage]
class ChatListRoute extends PageRouteInfo<void> {
  const ChatListRoute({List<PageRouteInfo>? children})
      : super(
          ChatListRoute.name,
          initialChildren: children,
        );

  static const String name = 'ChatListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ChatListPage();
    },
  );
}

/// generated route for
/// [ChatPage]
class ChatRoute extends PageRouteInfo<ChatRouteArgs> {
  ChatRoute({
    Key? key,
    required String? conversationId,
    String? createdAt,
    ChatItems? forwardMessage,
    String? defaultText,
    List<PageRouteInfo>? children,
  }) : super(
          ChatRoute.name,
          args: ChatRouteArgs(
            key: key,
            conversationId: conversationId,
            createdAt: createdAt,
            forwardMessage: forwardMessage,
            defaultText: defaultText,
          ),
          rawPathParams: {'conversationId': conversationId},
          rawQueryParams: {'createdAt': createdAt},
          initialChildren: children,
        );

  static const String name = 'ChatRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final queryParams = data.queryParams;
      final args = data.argsAs<ChatRouteArgs>(
          orElse: () => ChatRouteArgs(
                conversationId: pathParams.optString('conversationId'),
                createdAt: queryParams.optString('createdAt'),
              ));
      return ChatPage(
        key: args.key,
        conversationId: args.conversationId,
        createdAt: args.createdAt,
        forwardMessage: args.forwardMessage,
        defaultText: args.defaultText,
      );
    },
  );
}

class ChatRouteArgs {
  const ChatRouteArgs({
    this.key,
    required this.conversationId,
    this.createdAt,
    this.forwardMessage,
    this.defaultText,
  });

  final Key? key;

  final String? conversationId;

  final String? createdAt;

  final ChatItems? forwardMessage;

  final String? defaultText;

  @override
  String toString() {
    return 'ChatRouteArgs{key: $key, conversationId: $conversationId, createdAt: $createdAt, forwardMessage: $forwardMessage, defaultText: $defaultText}';
  }
}

/// generated route for
/// [ChatSelectBranchPage]
class ChatSelectBranchRoute extends PageRouteInfo<void> {
  const ChatSelectBranchRoute({List<PageRouteInfo>? children})
      : super(
          ChatSelectBranchRoute.name,
          initialChildren: children,
        );

  static const String name = 'ChatSelectBranchRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ChatSelectBranchPage();
    },
  );
}

/// generated route for
/// [CheckinPhotoPage]
class CheckinPhotoRoute extends PageRouteInfo<void> {
  const CheckinPhotoRoute({List<PageRouteInfo>? children})
      : super(
          CheckinPhotoRoute.name,
          initialChildren: children,
        );

  static const String name = 'CheckinPhotoRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CheckinPhotoPage();
    },
  );
}

/// generated route for
/// [CheckinReminderPage]
class CheckinReminderRoute extends PageRouteInfo<void> {
  const CheckinReminderRoute({List<PageRouteInfo>? children})
      : super(
          CheckinReminderRoute.name,
          initialChildren: children,
        );

  static const String name = 'CheckinReminderRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CheckinReminderPage();
    },
  );
}

/// generated route for
/// [ChooseAgencyPage]
class ChooseAgencyRoute extends PageRouteInfo<ChooseAgencyRouteArgs> {
  ChooseAgencyRoute({
    Key? key,
    required ChooseAgencyPageBundle chooseAgencyParams,
    List<PageRouteInfo>? children,
  }) : super(
          ChooseAgencyRoute.name,
          args: ChooseAgencyRouteArgs(
            key: key,
            chooseAgencyParams: chooseAgencyParams,
          ),
          initialChildren: children,
        );

  static const String name = 'ChooseAgencyRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ChooseAgencyRouteArgs>();
      return ChooseAgencyPage(
        key: args.key,
        chooseAgencyParams: args.chooseAgencyParams,
      );
    },
  );
}

class ChooseAgencyRouteArgs {
  const ChooseAgencyRouteArgs({
    this.key,
    required this.chooseAgencyParams,
  });

  final Key? key;

  final ChooseAgencyPageBundle chooseAgencyParams;

  @override
  String toString() {
    return 'ChooseAgencyRouteArgs{key: $key, chooseAgencyParams: $chooseAgencyParams}';
  }
}

/// generated route for
/// [CommentListUpdatePage]
class CommentListUpdateRoute extends PageRouteInfo<void> {
  const CommentListUpdateRoute({List<PageRouteInfo>? children})
      : super(
          CommentListUpdateRoute.name,
          initialChildren: children,
        );

  static const String name = 'CommentListUpdateRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CommentListUpdatePage();
    },
  );
}

/// generated route for
/// [ConfirmOtpPage]
class ConfirmOtpRoute extends PageRouteInfo<ConfirmOtpRouteArgs> {
  ConfirmOtpRoute({
    Key? key,
    required String phone,
    List<PageRouteInfo>? children,
  }) : super(
          ConfirmOtpRoute.name,
          args: ConfirmOtpRouteArgs(
            key: key,
            phone: phone,
          ),
          rawPathParams: {'id': phone},
          initialChildren: children,
        );

  static const String name = 'ConfirmOtpRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<ConfirmOtpRouteArgs>(
          orElse: () => ConfirmOtpRouteArgs(phone: pathParams.getString('id')));
      return ConfirmOtpPage(
        key: args.key,
        phone: args.phone,
      );
    },
  );
}

class ConfirmOtpRouteArgs {
  const ConfirmOtpRouteArgs({
    this.key,
    required this.phone,
  });

  final Key? key;

  final String phone;

  @override
  String toString() {
    return 'ConfirmOtpRouteArgs{key: $key, phone: $phone}';
  }
}

/// generated route for
/// [ConsultationCustomerPage]
class ConsultationCustomerRoute
    extends PageRouteInfo<ConsultationCustomerRouteArgs> {
  ConsultationCustomerRoute({
    required PxCustomer? customer,
    ConsultationService? service,
    required bool isUnassigned,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ConsultationCustomerRoute.name,
          args: ConsultationCustomerRouteArgs(
            customer: customer,
            service: service,
            isUnassigned: isUnassigned,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ConsultationCustomerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ConsultationCustomerRouteArgs>();
      return ConsultationCustomerPage(
        customer: args.customer,
        service: args.service,
        isUnassigned: args.isUnassigned,
        key: args.key,
      );
    },
  );
}

class ConsultationCustomerRouteArgs {
  const ConsultationCustomerRouteArgs({
    required this.customer,
    this.service,
    required this.isUnassigned,
    this.key,
  });

  final PxCustomer? customer;

  final ConsultationService? service;

  final bool isUnassigned;

  final Key? key;

  @override
  String toString() {
    return 'ConsultationCustomerRouteArgs{customer: $customer, service: $service, isUnassigned: $isUnassigned, key: $key}';
  }
}

/// generated route for
/// [ConsultationHistoryDetailPage]
class ConsultationHistoryDetailRoute
    extends PageRouteInfo<ConsultationHistoryDetailRouteArgs> {
  ConsultationHistoryDetailRoute({
    Key? key,
    CustomerConsultationHistoryModel? data,
    required String? customerId,
    List<PageRouteInfo>? children,
  }) : super(
          ConsultationHistoryDetailRoute.name,
          args: ConsultationHistoryDetailRouteArgs(
            key: key,
            data: data,
            customerId: customerId,
          ),
          initialChildren: children,
        );

  static const String name = 'ConsultationHistoryDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ConsultationHistoryDetailRouteArgs>();
      return ConsultationHistoryDetailPage(
        key: args.key,
        data: args.data,
        customerId: args.customerId,
      );
    },
  );
}

class ConsultationHistoryDetailRouteArgs {
  const ConsultationHistoryDetailRouteArgs({
    this.key,
    this.data,
    required this.customerId,
  });

  final Key? key;

  final CustomerConsultationHistoryModel? data;

  final String? customerId;

  @override
  String toString() {
    return 'ConsultationHistoryDetailRouteArgs{key: $key, data: $data, customerId: $customerId}';
  }
}

/// generated route for
/// [ConsultationHistoryPage]
class ConsultationHistoryRoute
    extends PageRouteInfo<ConsultationHistoryRouteArgs> {
  ConsultationHistoryRoute({
    Key? key,
    required String? customerId,
    List<PageRouteInfo>? children,
  }) : super(
          ConsultationHistoryRoute.name,
          args: ConsultationHistoryRouteArgs(
            key: key,
            customerId: customerId,
          ),
          initialChildren: children,
        );

  static const String name = 'ConsultationHistoryRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ConsultationHistoryRouteArgs>();
      return ConsultationHistoryPage(
        key: args.key,
        customerId: args.customerId,
      );
    },
  );
}

class ConsultationHistoryRouteArgs {
  const ConsultationHistoryRouteArgs({
    this.key,
    required this.customerId,
  });

  final Key? key;

  final String? customerId;

  @override
  String toString() {
    return 'ConsultationHistoryRouteArgs{key: $key, customerId: $customerId}';
  }
}

/// generated route for
/// [ConsultationManagerPage]
class ConsultationManagerRoute
    extends PageRouteInfo<ConsultationManagerRouteArgs> {
  ConsultationManagerRoute({
    bool isManager = false,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ConsultationManagerRoute.name,
          args: ConsultationManagerRouteArgs(
            isManager: isManager,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ConsultationManagerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ConsultationManagerRouteArgs>(
          orElse: () => const ConsultationManagerRouteArgs());
      return ConsultationManagerPage(
        isManager: args.isManager,
        key: args.key,
      );
    },
  );
}

class ConsultationManagerRouteArgs {
  const ConsultationManagerRouteArgs({
    this.isManager = false,
    this.key,
  });

  final bool isManager;

  final Key? key;

  @override
  String toString() {
    return 'ConsultationManagerRouteArgs{isManager: $isManager, key: $key}';
  }
}

/// generated route for
/// [CreateChatFolderPage]
class CreateChatFolderRoute extends PageRouteInfo<void> {
  const CreateChatFolderRoute({List<PageRouteInfo>? children})
      : super(
          CreateChatFolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'CreateChatFolderRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CreateChatFolderPage();
    },
  );
}

/// generated route for
/// [CreateChatGroupPage]
class CreateChatGroupRoute extends PageRouteInfo<void> {
  const CreateChatGroupRoute({List<PageRouteInfo>? children})
      : super(
          CreateChatGroupRoute.name,
          initialChildren: children,
        );

  static const String name = 'CreateChatGroupRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CreateChatGroupPage();
    },
  );
}

/// generated route for
/// [CreateCustomerPage]
class CreateCustomerRoute extends PageRouteInfo<CreateCustomerRouteArgs> {
  CreateCustomerRoute({
    Key? key,
    CustomerListItem? customer,
    String? phone,
    List<PageRouteInfo>? children,
  }) : super(
          CreateCustomerRoute.name,
          args: CreateCustomerRouteArgs(
            key: key,
            customer: customer,
            phone: phone,
          ),
          initialChildren: children,
        );

  static const String name = 'CreateCustomerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CreateCustomerRouteArgs>(
          orElse: () => const CreateCustomerRouteArgs());
      return CreateCustomerPage(
        key: args.key,
        customer: args.customer,
        phone: args.phone,
      );
    },
  );
}

class CreateCustomerRouteArgs {
  const CreateCustomerRouteArgs({
    this.key,
    this.customer,
    this.phone,
  });

  final Key? key;

  final CustomerListItem? customer;

  final String? phone;

  @override
  String toString() {
    return 'CreateCustomerRouteArgs{key: $key, customer: $customer, phone: $phone}';
  }
}

/// generated route for
/// [CreateSupportRequestsScreen]
class CreateSupportRequestsRoute extends PageRouteInfo<void> {
  const CreateSupportRequestsRoute({List<PageRouteInfo>? children})
      : super(
          CreateSupportRequestsRoute.name,
          initialChildren: children,
        );

  static const String name = 'CreateSupportRequestsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CreateSupportRequestsScreen();
    },
  );
}

/// generated route for
/// [CreatingEformScreen]
class CreatingEformRoute extends PageRouteInfo<CreatingEformRouteArgs> {
  CreatingEformRoute({
    Key? key,
    EformCategoryItemsModel? category,
    List<PageRouteInfo>? children,
  }) : super(
          CreatingEformRoute.name,
          args: CreatingEformRouteArgs(
            key: key,
            category: category,
          ),
          initialChildren: children,
        );

  static const String name = 'CreatingEformRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CreatingEformRouteArgs>(
          orElse: () => const CreatingEformRouteArgs());
      return CreatingEformScreen(
        key: args.key,
        category: args.category,
      );
    },
  );
}

class CreatingEformRouteArgs {
  const CreatingEformRouteArgs({
    this.key,
    this.category,
  });

  final Key? key;

  final EformCategoryItemsModel? category;

  @override
  String toString() {
    return 'CreatingEformRouteArgs{key: $key, category: $category}';
  }
}

/// generated route for
/// [CreatingTaskPage]
class CreatingTaskRoute extends PageRouteInfo<void> {
  const CreatingTaskRoute({List<PageRouteInfo>? children})
      : super(
          CreatingTaskRoute.name,
          initialChildren: children,
        );

  static const String name = 'CreatingTaskRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CreatingTaskPage();
    },
  );
}

/// generated route for
/// [CustomImageReviewPage]
class CustomImageReviewRoute extends PageRouteInfo<CustomImageReviewRouteArgs> {
  CustomImageReviewRoute({
    Key? key,
    required ValueNotifier<CustomerListItem?> customerLocal,
    required ValueNotifier<TicketCreatedTypeItems?> serviceLocal,
    required ValueNotifierList<TakingCareCustomerGetTreatmentPhotoItemsDetail>
        imageBefoLocal,
    required ValueNotifierList<TakingCareCustomerGetTreatmentPhotoItemsDetail>
        imageAfterLocal,
    required ValueNotifierList<String> imagesBefore,
    required ValueNotifierList<String> imagesAfter,
    List<PageRouteInfo>? children,
  }) : super(
          CustomImageReviewRoute.name,
          args: CustomImageReviewRouteArgs(
            key: key,
            customerLocal: customerLocal,
            serviceLocal: serviceLocal,
            imageBefoLocal: imageBefoLocal,
            imageAfterLocal: imageAfterLocal,
            imagesBefore: imagesBefore,
            imagesAfter: imagesAfter,
          ),
          initialChildren: children,
        );

  static const String name = 'CustomImageReviewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CustomImageReviewRouteArgs>();
      return CustomImageReviewPage(
        key: args.key,
        customerLocal: args.customerLocal,
        serviceLocal: args.serviceLocal,
        imageBefoLocal: args.imageBefoLocal,
        imageAfterLocal: args.imageAfterLocal,
        imagesBefore: args.imagesBefore,
        imagesAfter: args.imagesAfter,
      );
    },
  );
}

class CustomImageReviewRouteArgs {
  const CustomImageReviewRouteArgs({
    this.key,
    required this.customerLocal,
    required this.serviceLocal,
    required this.imageBefoLocal,
    required this.imageAfterLocal,
    required this.imagesBefore,
    required this.imagesAfter,
  });

  final Key? key;

  final ValueNotifier<CustomerListItem?> customerLocal;

  final ValueNotifier<TicketCreatedTypeItems?> serviceLocal;

  final ValueNotifierList<TakingCareCustomerGetTreatmentPhotoItemsDetail>
      imageBefoLocal;

  final ValueNotifierList<TakingCareCustomerGetTreatmentPhotoItemsDetail>
      imageAfterLocal;

  final ValueNotifierList<String> imagesBefore;

  final ValueNotifierList<String> imagesAfter;

  @override
  String toString() {
    return 'CustomImageReviewRouteArgs{key: $key, customerLocal: $customerLocal, serviceLocal: $serviceLocal, imageBefoLocal: $imageBefoLocal, imageAfterLocal: $imageAfterLocal, imagesBefore: $imagesBefore, imagesAfter: $imagesAfter}';
  }
}

/// generated route for
/// [CustomerBookingInfoPage]
class CustomerBookingInfoRoute
    extends PageRouteInfo<CustomerBookingInfoRouteArgs> {
  CustomerBookingInfoRoute({
    Key? key,
    required int? customerId,
    List<PageRouteInfo>? children,
  }) : super(
          CustomerBookingInfoRoute.name,
          args: CustomerBookingInfoRouteArgs(
            key: key,
            customerId: customerId,
          ),
          initialChildren: children,
        );

  static const String name = 'CustomerBookingInfoRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CustomerBookingInfoRouteArgs>();
      return CustomerBookingInfoPage(
        key: args.key,
        customerId: args.customerId,
      );
    },
  );
}

class CustomerBookingInfoRouteArgs {
  const CustomerBookingInfoRouteArgs({
    this.key,
    required this.customerId,
  });

  final Key? key;

  final int? customerId;

  @override
  String toString() {
    return 'CustomerBookingInfoRouteArgs{key: $key, customerId: $customerId}';
  }
}

/// generated route for
/// [CustomerInfoDetailsPage]
class CustomerInfoDetailsRoute
    extends PageRouteInfo<CustomerInfoDetailsRouteArgs> {
  CustomerInfoDetailsRoute({
    Key? key,
    required BranchItemsModel? branch,
    required CustomerInfoDetailsRequestParams params,
    List<PageRouteInfo>? children,
  }) : super(
          CustomerInfoDetailsRoute.name,
          args: CustomerInfoDetailsRouteArgs(
            key: key,
            branch: branch,
            params: params,
          ),
          initialChildren: children,
        );

  static const String name = 'CustomerInfoDetailsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CustomerInfoDetailsRouteArgs>();
      return CustomerInfoDetailsPage(
        key: args.key,
        branch: args.branch,
        params: args.params,
      );
    },
  );
}

class CustomerInfoDetailsRouteArgs {
  const CustomerInfoDetailsRouteArgs({
    this.key,
    required this.branch,
    required this.params,
  });

  final Key? key;

  final BranchItemsModel? branch;

  final CustomerInfoDetailsRequestParams params;

  @override
  String toString() {
    return 'CustomerInfoDetailsRouteArgs{key: $key, branch: $branch, params: $params}';
  }
}

/// generated route for
/// [CustomerListPage]
class CustomerListRoute extends PageRouteInfo<void> {
  const CustomerListRoute({List<PageRouteInfo>? children})
      : super(
          CustomerListRoute.name,
          initialChildren: children,
        );

  static const String name = 'CustomerListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CustomerListPage();
    },
  );
}

/// generated route for
/// [CustomerPage]
class CustomerRoute extends PageRouteInfo<CustomerRouteArgs> {
  CustomerRoute({
    Key? key,
    required String id,
    bool isScanQrCode = false,
    List<PageRouteInfo>? children,
  }) : super(
          CustomerRoute.name,
          args: CustomerRouteArgs(
            key: key,
            id: id,
            isScanQrCode: isScanQrCode,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'CustomerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<CustomerRouteArgs>(
          orElse: () => CustomerRouteArgs(id: pathParams.getString('id')));
      return CustomerPage(
        key: args.key,
        id: args.id,
        isScanQrCode: args.isScanQrCode,
      );
    },
  );
}

class CustomerRouteArgs {
  const CustomerRouteArgs({
    this.key,
    required this.id,
    this.isScanQrCode = false,
  });

  final Key? key;

  final String id;

  final bool isScanQrCode;

  @override
  String toString() {
    return 'CustomerRouteArgs{key: $key, id: $id, isScanQrCode: $isScanQrCode}';
  }
}

/// generated route for
/// [CustomerProfilePage]
class CustomerProfileRoute extends PageRouteInfo<CustomerProfileRouteArgs> {
  CustomerProfileRoute({
    required String? customerID,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          CustomerProfileRoute.name,
          args: CustomerProfileRouteArgs(
            customerID: customerID,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'CustomerProfileRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CustomerProfileRouteArgs>();
      return CustomerProfilePage(
        args.customerID,
        key: args.key,
      );
    },
  );
}

class CustomerProfileRouteArgs {
  const CustomerProfileRouteArgs({
    required this.customerID,
    this.key,
  });

  final String? customerID;

  final Key? key;

  @override
  String toString() {
    return 'CustomerProfileRouteArgs{customerID: $customerID, key: $key}';
  }
}

/// generated route for
/// [CustomerRecordPage]
class CustomerRecordRoute extends PageRouteInfo<CustomerRecordRouteArgs> {
  CustomerRecordRoute({
    Key? key,
    required CustomerListItem? customer,
    List<PageRouteInfo>? children,
  }) : super(
          CustomerRecordRoute.name,
          args: CustomerRecordRouteArgs(
            key: key,
            customer: customer,
          ),
          initialChildren: children,
        );

  static const String name = 'CustomerRecordRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CustomerRecordRouteArgs>();
      return CustomerRecordPage(
        key: args.key,
        customer: args.customer,
      );
    },
  );
}

class CustomerRecordRouteArgs {
  const CustomerRecordRouteArgs({
    this.key,
    required this.customer,
  });

  final Key? key;

  final CustomerListItem? customer;

  @override
  String toString() {
    return 'CustomerRecordRouteArgs{key: $key, customer: $customer}';
  }
}

/// generated route for
/// [CustomerSchedulePage]
class CustomerScheduleRoute extends PageRouteInfo<CustomerScheduleRouteArgs> {
  CustomerScheduleRoute({
    required bool isLeader,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          CustomerScheduleRoute.name,
          args: CustomerScheduleRouteArgs(
            isLeader: isLeader,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'CustomerScheduleRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CustomerScheduleRouteArgs>();
      return CustomerSchedulePage(
        isLeader: args.isLeader,
        key: args.key,
      );
    },
  );
}

class CustomerScheduleRouteArgs {
  const CustomerScheduleRouteArgs({
    required this.isLeader,
    this.key,
  });

  final bool isLeader;

  final Key? key;

  @override
  String toString() {
    return 'CustomerScheduleRouteArgs{isLeader: $isLeader, key: $key}';
  }
}

/// generated route for
/// [DetailCrmCustomerPage]
class DetailCrmCustomerRoute extends PageRouteInfo<DetailCrmCustomerRouteArgs> {
  DetailCrmCustomerRoute({
    Key? key,
    required String? customerCrmId,
    required int? ticketId,
    required String? ticketTeam,
    List<PageRouteInfo>? children,
  }) : super(
          DetailCrmCustomerRoute.name,
          args: DetailCrmCustomerRouteArgs(
            key: key,
            customerCrmId: customerCrmId,
            ticketId: ticketId,
            ticketTeam: ticketTeam,
          ),
          rawPathParams: {
            'customerCrmId': customerCrmId,
            'ticketId': ticketId,
            'ticketTeam': ticketTeam,
          },
          initialChildren: children,
        );

  static const String name = 'DetailCrmCustomerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<DetailCrmCustomerRouteArgs>(
          orElse: () => DetailCrmCustomerRouteArgs(
                customerCrmId: pathParams.optString('customerCrmId'),
                ticketId: pathParams.optInt('ticketId'),
                ticketTeam: pathParams.optString('ticketTeam'),
              ));
      return DetailCrmCustomerPage(
        key: args.key,
        customerCrmId: args.customerCrmId,
        ticketId: args.ticketId,
        ticketTeam: args.ticketTeam,
      );
    },
  );
}

class DetailCrmCustomerRouteArgs {
  const DetailCrmCustomerRouteArgs({
    this.key,
    required this.customerCrmId,
    required this.ticketId,
    required this.ticketTeam,
  });

  final Key? key;

  final String? customerCrmId;

  final int? ticketId;

  final String? ticketTeam;

  @override
  String toString() {
    return 'DetailCrmCustomerRouteArgs{key: $key, customerCrmId: $customerCrmId, ticketId: $ticketId, ticketTeam: $ticketTeam}';
  }
}

/// generated route for
/// [DetailEformPage]
class DetailEformRoute extends PageRouteInfo<DetailEformRouteArgs> {
  DetailEformRoute({
    Key? key,
    required DetailEFormBundle detailEFormBundle,
    List<PageRouteInfo>? children,
  }) : super(
          DetailEformRoute.name,
          args: DetailEformRouteArgs(
            key: key,
            detailEFormBundle: detailEFormBundle,
          ),
          initialChildren: children,
        );

  static const String name = 'DetailEformRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<DetailEformRouteArgs>();
      return DetailEformPage(
        key: args.key,
        detailEFormBundle: args.detailEFormBundle,
      );
    },
  );
}

class DetailEformRouteArgs {
  const DetailEformRouteArgs({
    this.key,
    required this.detailEFormBundle,
  });

  final Key? key;

  final DetailEFormBundle detailEFormBundle;

  @override
  String toString() {
    return 'DetailEformRouteArgs{key: $key, detailEFormBundle: $detailEFormBundle}';
  }
}

/// generated route for
/// [DetailJobSchedulerPage]
class DetailJobSchedulerRoute
    extends PageRouteInfo<DetailJobSchedulerRouteArgs> {
  DetailJobSchedulerRoute({
    Key? key,
    String? id,
    List<PageRouteInfo>? children,
  }) : super(
          DetailJobSchedulerRoute.name,
          args: DetailJobSchedulerRouteArgs(
            key: key,
            id: id,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'DetailJobSchedulerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<DetailJobSchedulerRouteArgs>(
          orElse: () =>
              DetailJobSchedulerRouteArgs(id: pathParams.optString('id')));
      return DetailJobSchedulerPage(
        key: args.key,
        id: args.id,
      );
    },
  );
}

class DetailJobSchedulerRouteArgs {
  const DetailJobSchedulerRouteArgs({
    this.key,
    this.id,
  });

  final Key? key;

  final String? id;

  @override
  String toString() {
    return 'DetailJobSchedulerRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [DetailNewsPage]
class DetailNewsRoute extends PageRouteInfo<DetailNewsRouteArgs> {
  DetailNewsRoute({
    Key? key,
    String? id,
    List<PageRouteInfo>? children,
  }) : super(
          DetailNewsRoute.name,
          args: DetailNewsRouteArgs(
            key: key,
            id: id,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'DetailNewsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<DetailNewsRouteArgs>(
          orElse: () => DetailNewsRouteArgs(id: pathParams.optString('id')));
      return DetailNewsPage(
        key: args.key,
        id: args.id,
      );
    },
  );
}

class DetailNewsRouteArgs {
  const DetailNewsRouteArgs({
    this.key,
    this.id,
  });

  final Key? key;

  final String? id;

  @override
  String toString() {
    return 'DetailNewsRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [DetailNotificationPage]
class DetailNotificationRoute
    extends PageRouteInfo<DetailNotificationRouteArgs> {
  DetailNotificationRoute({
    Key? key,
    String? id,
    List<PageRouteInfo>? children,
  }) : super(
          DetailNotificationRoute.name,
          args: DetailNotificationRouteArgs(
            key: key,
            id: id,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'DetailNotificationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<DetailNotificationRouteArgs>(
          orElse: () =>
              DetailNotificationRouteArgs(id: pathParams.optString('id')));
      return DetailNotificationPage(
        key: args.key,
        id: args.id,
      );
    },
  );
}

class DetailNotificationRouteArgs {
  const DetailNotificationRouteArgs({
    this.key,
    this.id,
  });

  final Key? key;

  final String? id;

  @override
  String toString() {
    return 'DetailNotificationRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [DetailServicePage]
class DetailServiceRoute extends PageRouteInfo<DetailServiceRouteArgs> {
  DetailServiceRoute({
    Key? key,
    required ServiceAndProductItem? detail,
    List<PageRouteInfo>? children,
  }) : super(
          DetailServiceRoute.name,
          args: DetailServiceRouteArgs(
            key: key,
            detail: detail,
          ),
          initialChildren: children,
        );

  static const String name = 'DetailServiceRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<DetailServiceRouteArgs>();
      return DetailServicePage(
        key: args.key,
        detail: args.detail,
      );
    },
  );
}

class DetailServiceRouteArgs {
  const DetailServiceRouteArgs({
    this.key,
    required this.detail,
  });

  final Key? key;

  final ServiceAndProductItem? detail;

  @override
  String toString() {
    return 'DetailServiceRouteArgs{key: $key, detail: $detail}';
  }
}

/// generated route for
/// [DetailStaffEvaluationPeriodPage]
class DetailStaffEvaluationPeriodRoute
    extends PageRouteInfo<DetailStaffEvaluationPeriodRouteArgs> {
  DetailStaffEvaluationPeriodRoute({
    Key? key,
    required String? periodId,
    required String? positionId,
    String? title,
    List<PageRouteInfo>? children,
  }) : super(
          DetailStaffEvaluationPeriodRoute.name,
          args: DetailStaffEvaluationPeriodRouteArgs(
            key: key,
            periodId: periodId,
            positionId: positionId,
            title: title,
          ),
          rawPathParams: {
            'periodId': periodId,
            'positionId': positionId,
          },
          rawQueryParams: {'title': title},
          initialChildren: children,
        );

  static const String name = 'DetailStaffEvaluationPeriodRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final queryParams = data.queryParams;
      final args = data.argsAs<DetailStaffEvaluationPeriodRouteArgs>(
          orElse: () => DetailStaffEvaluationPeriodRouteArgs(
                periodId: pathParams.optString('periodId'),
                positionId: pathParams.optString('positionId'),
                title: queryParams.optString('title'),
              ));
      return DetailStaffEvaluationPeriodPage(
        key: args.key,
        periodId: args.periodId,
        positionId: args.positionId,
        title: args.title,
      );
    },
  );
}

class DetailStaffEvaluationPeriodRouteArgs {
  const DetailStaffEvaluationPeriodRouteArgs({
    this.key,
    required this.periodId,
    required this.positionId,
    this.title,
  });

  final Key? key;

  final String? periodId;

  final String? positionId;

  final String? title;

  @override
  String toString() {
    return 'DetailStaffEvaluationPeriodRouteArgs{key: $key, periodId: $periodId, positionId: $positionId, title: $title}';
  }
}

/// generated route for
/// [DevPage]
class DevRoute extends PageRouteInfo<void> {
  const DevRoute({List<PageRouteInfo>? children})
      : super(
          DevRoute.name,
          initialChildren: children,
        );

  static const String name = 'DevRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const DevPage();
    },
  );
}

/// generated route for
/// [EditHomeMenuScreen]
class EditHomeMenuRoute extends PageRouteInfo<void> {
  const EditHomeMenuRoute({List<PageRouteInfo>? children})
      : super(
          EditHomeMenuRoute.name,
          initialChildren: children,
        );

  static const String name = 'EditHomeMenuRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const EditHomeMenuScreen();
    },
  );
}

/// generated route for
/// [EformPage]
class EformRoute extends PageRouteInfo<void> {
  const EformRoute({List<PageRouteInfo>? children})
      : super(
          EformRoute.name,
          initialChildren: children,
        );

  static const String name = 'EformRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const EformPage();
    },
  );
}

/// generated route for
/// [EventActionsPage]
class EventActionsRoute extends PageRouteInfo<EventActionsRouteArgs> {
  EventActionsRoute({
    Key? key,
    required NDEventModel eventData,
    List<PageRouteInfo>? children,
  }) : super(
          EventActionsRoute.name,
          args: EventActionsRouteArgs(
            key: key,
            eventData: eventData,
          ),
          initialChildren: children,
        );

  static const String name = 'EventActionsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EventActionsRouteArgs>();
      return EventActionsPage(
        key: args.key,
        eventData: args.eventData,
      );
    },
  );
}

class EventActionsRouteArgs {
  const EventActionsRouteArgs({
    this.key,
    required this.eventData,
  });

  final Key? key;

  final NDEventModel eventData;

  @override
  String toString() {
    return 'EventActionsRouteArgs{key: $key, eventData: $eventData}';
  }
}

/// generated route for
/// [EventCheckInPage]
class EventCheckInRoute extends PageRouteInfo<EventCheckInRouteArgs> {
  EventCheckInRoute({
    Key? key,
    required NDEventExtraModel? data,
    List<PageRouteInfo>? children,
  }) : super(
          EventCheckInRoute.name,
          args: EventCheckInRouteArgs(
            key: key,
            data: data,
          ),
          initialChildren: children,
        );

  static const String name = 'EventCheckInRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EventCheckInRouteArgs>();
      return EventCheckInPage(
        key: args.key,
        data: args.data,
      );
    },
  );
}

class EventCheckInRouteArgs {
  const EventCheckInRouteArgs({
    this.key,
    required this.data,
  });

  final Key? key;

  final NDEventExtraModel? data;

  @override
  String toString() {
    return 'EventCheckInRouteArgs{key: $key, data: $data}';
  }
}

/// generated route for
/// [EventCheckOutPage]
class EventCheckOutRoute extends PageRouteInfo<EventCheckOutRouteArgs> {
  EventCheckOutRoute({
    Key? key,
    NDEventExtraModel? data,
    List<PageRouteInfo>? children,
  }) : super(
          EventCheckOutRoute.name,
          args: EventCheckOutRouteArgs(
            key: key,
            data: data,
          ),
          initialChildren: children,
        );

  static const String name = 'EventCheckOutRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EventCheckOutRouteArgs>(
          orElse: () => const EventCheckOutRouteArgs());
      return EventCheckOutPage(
        key: args.key,
        data: args.data,
      );
    },
  );
}

class EventCheckOutRouteArgs {
  const EventCheckOutRouteArgs({
    this.key,
    this.data,
  });

  final Key? key;

  final NDEventExtraModel? data;

  @override
  String toString() {
    return 'EventCheckOutRouteArgs{key: $key, data: $data}';
  }
}

/// generated route for
/// [EventConfirmOTPPage]
class EventConfirmOTPRoute extends PageRouteInfo<EventConfirmOTPRouteArgs> {
  EventConfirmOTPRoute({
    Key? key,
    required String phoneNumber,
    List<PageRouteInfo>? children,
  }) : super(
          EventConfirmOTPRoute.name,
          args: EventConfirmOTPRouteArgs(
            key: key,
            phoneNumber: phoneNumber,
          ),
          rawPathParams: {'id': phoneNumber},
          initialChildren: children,
        );

  static const String name = 'EventConfirmOTPRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<EventConfirmOTPRouteArgs>(
          orElse: () => EventConfirmOTPRouteArgs(
              phoneNumber: pathParams.getString('id')));
      return EventConfirmOTPPage(
        key: args.key,
        phoneNumber: args.phoneNumber,
      );
    },
  );
}

class EventConfirmOTPRouteArgs {
  const EventConfirmOTPRouteArgs({
    this.key,
    required this.phoneNumber,
  });

  final Key? key;

  final String phoneNumber;

  @override
  String toString() {
    return 'EventConfirmOTPRouteArgs{key: $key, phoneNumber: $phoneNumber}';
  }
}

/// generated route for
/// [EventHistoryPage]
class EventHistoryRoute extends PageRouteInfo<EventHistoryRouteArgs> {
  EventHistoryRoute({
    Key? key,
    required NDEventExtraModel data,
    List<PageRouteInfo>? children,
  }) : super(
          EventHistoryRoute.name,
          args: EventHistoryRouteArgs(
            key: key,
            data: data,
          ),
          initialChildren: children,
        );

  static const String name = 'EventHistoryRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EventHistoryRouteArgs>();
      return EventHistoryPage(
        key: args.key,
        data: args.data,
      );
    },
  );
}

class EventHistoryRouteArgs {
  const EventHistoryRouteArgs({
    this.key,
    required this.data,
  });

  final Key? key;

  final NDEventExtraModel data;

  @override
  String toString() {
    return 'EventHistoryRouteArgs{key: $key, data: $data}';
  }
}

/// generated route for
/// [EventLoginPage]
class EventLoginRoute extends PageRouteInfo<EventLoginRouteArgs> {
  EventLoginRoute({
    Key? key,
    bool isShowButtonClose = true,
    List<PageRouteInfo>? children,
  }) : super(
          EventLoginRoute.name,
          args: EventLoginRouteArgs(
            key: key,
            isShowButtonClose: isShowButtonClose,
          ),
          initialChildren: children,
        );

  static const String name = 'EventLoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EventLoginRouteArgs>(
          orElse: () => const EventLoginRouteArgs());
      return EventLoginPage(
        key: args.key,
        isShowButtonClose: args.isShowButtonClose,
      );
    },
  );
}

class EventLoginRouteArgs {
  const EventLoginRouteArgs({
    this.key,
    this.isShowButtonClose = true,
  });

  final Key? key;

  final bool isShowButtonClose;

  @override
  String toString() {
    return 'EventLoginRouteArgs{key: $key, isShowButtonClose: $isShowButtonClose}';
  }
}

/// generated route for
/// [EventRewardPage]
class EventRewardRoute extends PageRouteInfo<EventRewardRouteArgs> {
  EventRewardRoute({
    Key? key,
    NDEventExtraModel? data,
    List<PageRouteInfo>? children,
  }) : super(
          EventRewardRoute.name,
          args: EventRewardRouteArgs(
            key: key,
            data: data,
          ),
          initialChildren: children,
        );

  static const String name = 'EventRewardRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EventRewardRouteArgs>(
          orElse: () => const EventRewardRouteArgs());
      return EventRewardPage(
        key: args.key,
        data: args.data,
      );
    },
  );
}

class EventRewardRouteArgs {
  const EventRewardRouteArgs({
    this.key,
    this.data,
  });

  final Key? key;

  final NDEventExtraModel? data;

  @override
  String toString() {
    return 'EventRewardRouteArgs{key: $key, data: $data}';
  }
}

/// generated route for
/// [EventSplashPage]
class EventSplashRoute extends PageRouteInfo<void> {
  const EventSplashRoute({List<PageRouteInfo>? children})
      : super(
          EventSplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'EventSplashRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const EventSplashPage();
    },
  );
}

/// generated route for
/// [EventUnmapPage]
class EventUnmapRoute extends PageRouteInfo<EventUnmapRouteArgs> {
  EventUnmapRoute({
    Key? key,
    NDEventExtraModel? data,
    List<PageRouteInfo>? children,
  }) : super(
          EventUnmapRoute.name,
          args: EventUnmapRouteArgs(
            key: key,
            data: data,
          ),
          initialChildren: children,
        );

  static const String name = 'EventUnmapRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EventUnmapRouteArgs>(
          orElse: () => const EventUnmapRouteArgs());
      return EventUnmapPage(
        key: args.key,
        data: args.data,
      );
    },
  );
}

class EventUnmapRouteArgs {
  const EventUnmapRouteArgs({
    this.key,
    this.data,
  });

  final Key? key;

  final NDEventExtraModel? data;

  @override
  String toString() {
    return 'EventUnmapRouteArgs{key: $key, data: $data}';
  }
}

/// generated route for
/// [FontsSettingsScreen]
class FontsSettingsRoute extends PageRouteInfo<void> {
  const FontsSettingsRoute({List<PageRouteInfo>? children})
      : super(
          FontsSettingsRoute.name,
          initialChildren: children,
        );

  static const String name = 'FontsSettingsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const FontsSettingsScreen();
    },
  );
}

/// generated route for
/// [FunctionRoomScreen]
class FunctionRoomRoute extends PageRouteInfo<FunctionRoomRouteArgs> {
  FunctionRoomRoute({
    Key? key,
    String? id,
    List<PageRouteInfo>? children,
  }) : super(
          FunctionRoomRoute.name,
          args: FunctionRoomRouteArgs(
            key: key,
            id: id,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'FunctionRoomRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<FunctionRoomRouteArgs>(
          orElse: () => FunctionRoomRouteArgs(id: pathParams.optString('id')));
      return FunctionRoomScreen(
        key: args.key,
        id: args.id,
      );
    },
  );
}

class FunctionRoomRouteArgs {
  const FunctionRoomRouteArgs({
    this.key,
    this.id,
  });

  final Key? key;

  final String? id;

  @override
  String toString() {
    return 'FunctionRoomRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [GroupChatDetailPage]
class GroupChatDetailRoute extends PageRouteInfo<GroupChatDetailRouteArgs> {
  GroupChatDetailRoute({
    Key? key,
    required ChatListItems? conversation,
    List<PageRouteInfo>? children,
  }) : super(
          GroupChatDetailRoute.name,
          args: GroupChatDetailRouteArgs(
            key: key,
            conversation: conversation,
          ),
          initialChildren: children,
        );

  static const String name = 'GroupChatDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<GroupChatDetailRouteArgs>();
      return GroupChatDetailPage(
        key: args.key,
        conversation: args.conversation,
      );
    },
  );
}

class GroupChatDetailRouteArgs {
  const GroupChatDetailRouteArgs({
    this.key,
    required this.conversation,
  });

  final Key? key;

  final ChatListItems? conversation;

  @override
  String toString() {
    return 'GroupChatDetailRouteArgs{key: $key, conversation: $conversation}';
  }
}

/// generated route for
/// [HistoryCheckinPage]
class HistoryCheckinRoute extends PageRouteInfo<void> {
  const HistoryCheckinRoute({List<PageRouteInfo>? children})
      : super(
          HistoryCheckinRoute.name,
          initialChildren: children,
        );

  static const String name = 'HistoryCheckinRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const HistoryCheckinPage();
    },
  );
}

/// generated route for
/// [HomeFindPage]
class HomeFindRoute extends PageRouteInfo<void> {
  const HomeFindRoute({List<PageRouteInfo>? children})
      : super(
          HomeFindRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeFindRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const HomeFindPage();
    },
  );
}

/// generated route for
/// [HomeScreen]
class HomeRoute extends PageRouteInfo<HomeRouteArgs> {
  HomeRoute({
    Key? key,
    required AccLoginResponseModel? loginModel,
    List<PageRouteInfo>? children,
  }) : super(
          HomeRoute.name,
          args: HomeRouteArgs(
            key: key,
            loginModel: loginModel,
          ),
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<HomeRouteArgs>();
      return HomeScreen(
        key: args.key,
        loginModel: args.loginModel,
      );
    },
  );
}

class HomeRouteArgs {
  const HomeRouteArgs({
    this.key,
    required this.loginModel,
  });

  final Key? key;

  final AccLoginResponseModel? loginModel;

  @override
  String toString() {
    return 'HomeRouteArgs{key: $key, loginModel: $loginModel}';
  }
}

/// generated route for
/// [HrOrganizationPage]
class HrOrganizationRoute extends PageRouteInfo<HrOrganizationRouteArgs> {
  HrOrganizationRoute({
    Key? key,
    required String toNextPage,
    List<PageRouteInfo>? children,
  }) : super(
          HrOrganizationRoute.name,
          args: HrOrganizationRouteArgs(
            key: key,
            toNextPage: toNextPage,
          ),
          rawPathParams: {'toNextPage': toNextPage},
          initialChildren: children,
        );

  static const String name = 'HrOrganizationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<HrOrganizationRouteArgs>(
          orElse: () => HrOrganizationRouteArgs(
              toNextPage: pathParams.getString('toNextPage')));
      return HrOrganizationPage(
        key: args.key,
        toNextPage: args.toNextPage,
      );
    },
  );
}

class HrOrganizationRouteArgs {
  const HrOrganizationRouteArgs({
    this.key,
    required this.toNextPage,
  });

  final Key? key;

  final String toNextPage;

  @override
  String toString() {
    return 'HrOrganizationRouteArgs{key: $key, toNextPage: $toNextPage}';
  }
}

/// generated route for
/// [ImagePreviewPage]
class ImagePreviewRoute extends PageRouteInfo<ImagePreviewRouteArgs> {
  ImagePreviewRoute({
    Key? key,
    Widget Function(
      BuildContext,
      ImageChunkEvent?,
    )? loadingBuilder,
    BoxDecoration? backgroundDecoration,
    int initialIndex = 0,
    required List<String?> images,
    String? heroTag,
    Axis scrollDirection = Axis.horizontal,
    List<PageRouteInfo>? children,
  }) : super(
          ImagePreviewRoute.name,
          args: ImagePreviewRouteArgs(
            key: key,
            loadingBuilder: loadingBuilder,
            backgroundDecoration: backgroundDecoration,
            initialIndex: initialIndex,
            images: images,
            heroTag: heroTag,
            scrollDirection: scrollDirection,
          ),
          initialChildren: children,
        );

  static const String name = 'ImagePreviewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ImagePreviewRouteArgs>();
      return ImagePreviewPage(
        key: args.key,
        loadingBuilder: args.loadingBuilder,
        backgroundDecoration: args.backgroundDecoration,
        initialIndex: args.initialIndex,
        images: args.images,
        heroTag: args.heroTag,
        scrollDirection: args.scrollDirection,
      );
    },
  );
}

class ImagePreviewRouteArgs {
  const ImagePreviewRouteArgs({
    this.key,
    this.loadingBuilder,
    this.backgroundDecoration,
    this.initialIndex = 0,
    required this.images,
    this.heroTag,
    this.scrollDirection = Axis.horizontal,
  });

  final Key? key;

  final Widget Function(
    BuildContext,
    ImageChunkEvent?,
  )? loadingBuilder;

  final BoxDecoration? backgroundDecoration;

  final int initialIndex;

  final List<String?> images;

  final String? heroTag;

  final Axis scrollDirection;

  @override
  String toString() {
    return 'ImagePreviewRouteArgs{key: $key, loadingBuilder: $loadingBuilder, backgroundDecoration: $backgroundDecoration, initialIndex: $initialIndex, images: $images, heroTag: $heroTag, scrollDirection: $scrollDirection}';
  }
}

/// generated route for
/// [ImportantNotesPage]
class ImportantNotesRoute extends PageRouteInfo<ImportantNotesRouteArgs> {
  ImportantNotesRoute({
    required String? customerID,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ImportantNotesRoute.name,
          args: ImportantNotesRouteArgs(
            customerID: customerID,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ImportantNotesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ImportantNotesRouteArgs>();
      return ImportantNotesPage(
        args.customerID,
        key: args.key,
      );
    },
  );
}

class ImportantNotesRouteArgs {
  const ImportantNotesRouteArgs({
    required this.customerID,
    this.key,
  });

  final String? customerID;

  final Key? key;

  @override
  String toString() {
    return 'ImportantNotesRouteArgs{customerID: $customerID, key: $key}';
  }
}

/// generated route for
/// [InfoCustomerScreen]
class InfoCustomerRoute extends PageRouteInfo<InfoCustomerRouteArgs> {
  InfoCustomerRoute({
    Key? key,
    required String? id,
    List<PageRouteInfo>? children,
  }) : super(
          InfoCustomerRoute.name,
          args: InfoCustomerRouteArgs(
            key: key,
            id: id,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'InfoCustomerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<InfoCustomerRouteArgs>(
          orElse: () => InfoCustomerRouteArgs(id: pathParams.optString('id')));
      return InfoCustomerScreen(
        key: args.key,
        id: args.id,
      );
    },
  );
}

class InfoCustomerRouteArgs {
  const InfoCustomerRouteArgs({
    this.key,
    required this.id,
  });

  final Key? key;

  final String? id;

  @override
  String toString() {
    return 'InfoCustomerRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [InputAssetCodeScreen]
class InputAssetCodeRoute extends PageRouteInfo<void> {
  const InputAssetCodeRoute({List<PageRouteInfo>? children})
      : super(
          InputAssetCodeRoute.name,
          initialChildren: children,
        );

  static const String name = 'InputAssetCodeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const InputAssetCodeScreen();
    },
  );
}

/// generated route for
/// [InputInventoryBarCodeScreen]
class InputInventoryBarCodeRoute
    extends PageRouteInfo<InputInventoryBarCodeRouteArgs> {
  InputInventoryBarCodeRoute({
    Key? key,
    required InputParamsInventory inputParams,
    List<PageRouteInfo>? children,
  }) : super(
          InputInventoryBarCodeRoute.name,
          args: InputInventoryBarCodeRouteArgs(
            key: key,
            inputParams: inputParams,
          ),
          initialChildren: children,
        );

  static const String name = 'InputInventoryBarCodeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<InputInventoryBarCodeRouteArgs>();
      return InputInventoryBarCodeScreen(
        key: args.key,
        inputParams: args.inputParams,
      );
    },
  );
}

class InputInventoryBarCodeRouteArgs {
  const InputInventoryBarCodeRouteArgs({
    this.key,
    required this.inputParams,
  });

  final Key? key;

  final InputParamsInventory inputParams;

  @override
  String toString() {
    return 'InputInventoryBarCodeRouteArgs{key: $key, inputParams: $inputParams}';
  }
}

/// generated route for
/// [InventoryTicketFormPage]
class InventoryTicketFormRoute
    extends PageRouteInfo<InventoryTicketFormRouteArgs> {
  InventoryTicketFormRoute({
    Key? key,
    ScrollController? scrollController,
    List<PageRouteInfo>? children,
  }) : super(
          InventoryTicketFormRoute.name,
          args: InventoryTicketFormRouteArgs(
            key: key,
            scrollController: scrollController,
          ),
          initialChildren: children,
        );

  static const String name = 'InventoryTicketFormRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<InventoryTicketFormRouteArgs>(
          orElse: () => const InventoryTicketFormRouteArgs());
      return InventoryTicketFormPage(
        key: args.key,
        scrollController: args.scrollController,
      );
    },
  );
}

class InventoryTicketFormRouteArgs {
  const InventoryTicketFormRouteArgs({
    this.key,
    this.scrollController,
  });

  final Key? key;

  final ScrollController? scrollController;

  @override
  String toString() {
    return 'InventoryTicketFormRouteArgs{key: $key, scrollController: $scrollController}';
  }
}

/// generated route for
/// [InventoryTicketScreen]
class InventoryTicketRoute extends PageRouteInfo<InventoryTicketRouteArgs> {
  InventoryTicketRoute({
    Key? key,
    required InventoryModel inventoryData,
    List<PageRouteInfo>? children,
  }) : super(
          InventoryTicketRoute.name,
          args: InventoryTicketRouteArgs(
            key: key,
            inventoryData: inventoryData,
          ),
          initialChildren: children,
        );

  static const String name = 'InventoryTicketRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<InventoryTicketRouteArgs>();
      return InventoryTicketScreen(
        key: args.key,
        inventoryData: args.inventoryData,
      );
    },
  );
}

class InventoryTicketRouteArgs {
  const InventoryTicketRouteArgs({
    this.key,
    required this.inventoryData,
  });

  final Key? key;

  final InventoryModel inventoryData;

  @override
  String toString() {
    return 'InventoryTicketRouteArgs{key: $key, inventoryData: $inventoryData}';
  }
}

/// generated route for
/// [JobSchedulerScreen]
class JobSchedulerRoute extends PageRouteInfo<void> {
  const JobSchedulerRoute({List<PageRouteInfo>? children})
      : super(
          JobSchedulerRoute.name,
          initialChildren: children,
        );

  static const String name = 'JobSchedulerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const JobSchedulerScreen();
    },
  );
}

/// generated route for
/// [KpiEmployeeDetailPage]
class KpiEmployeeDetailRoute extends PageRouteInfo<KpiEmployeeDetailRouteArgs> {
  KpiEmployeeDetailRoute({
    Key? key,
    required DateTime date,
    List<PageRouteInfo>? children,
  }) : super(
          KpiEmployeeDetailRoute.name,
          args: KpiEmployeeDetailRouteArgs(
            key: key,
            date: date,
          ),
          initialChildren: children,
        );

  static const String name = 'KpiEmployeeDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<KpiEmployeeDetailRouteArgs>();
      return KpiEmployeeDetailPage(
        key: args.key,
        date: args.date,
      );
    },
  );
}

class KpiEmployeeDetailRouteArgs {
  const KpiEmployeeDetailRouteArgs({
    this.key,
    required this.date,
  });

  final Key? key;

  final DateTime date;

  @override
  String toString() {
    return 'KpiEmployeeDetailRouteArgs{key: $key, date: $date}';
  }
}

/// generated route for
/// [KpiEmployeePage]
class KpiEmployeeRoute extends PageRouteInfo<void> {
  const KpiEmployeeRoute({List<PageRouteInfo>? children})
      : super(
          KpiEmployeeRoute.name,
          initialChildren: children,
        );

  static const String name = 'KpiEmployeeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const KpiEmployeePage();
    },
  );
}

/// generated route for
/// [LandingPage]
class LandingRoute extends PageRouteInfo<LandingRouteArgs> {
  LandingRoute({
    Key? key,
    App app = App.THEADVANCE,
    List<PageRouteInfo>? children,
  }) : super(
          LandingRoute.name,
          args: LandingRouteArgs(
            key: key,
            app: app,
          ),
          initialChildren: children,
        );

  static const String name = 'LandingRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<LandingRouteArgs>(orElse: () => const LandingRouteArgs());
      return LandingPage(
        key: args.key,
        app: args.app,
      );
    },
  );
}

class LandingRouteArgs {
  const LandingRouteArgs({
    this.key,
    this.app = App.THEADVANCE,
  });

  final Key? key;

  final App app;

  @override
  String toString() {
    return 'LandingRouteArgs{key: $key, app: $app}';
  }
}

/// generated route for
/// [LanguageScreen]
class LanguageRoute extends PageRouteInfo<void> {
  const LanguageRoute({List<PageRouteInfo>? children})
      : super(
          LanguageRoute.name,
          initialChildren: children,
        );

  static const String name = 'LanguageRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const LanguageScreen();
    },
  );
}

/// generated route for
/// [ListCustomerPage]
class ListCustomerRoute extends PageRouteInfo<ListCustomerRouteArgs> {
  ListCustomerRoute({
    Key? key,
    required FloorModel? floor,
    required BranchItemsModel? branch,
    List<PageRouteInfo>? children,
  }) : super(
          ListCustomerRoute.name,
          args: ListCustomerRouteArgs(
            key: key,
            floor: floor,
            branch: branch,
          ),
          initialChildren: children,
        );

  static const String name = 'ListCustomerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ListCustomerRouteArgs>();
      return ListCustomerPage(
        key: args.key,
        floor: args.floor,
        branch: args.branch,
      );
    },
  );
}

class ListCustomerRouteArgs {
  const ListCustomerRouteArgs({
    this.key,
    required this.floor,
    required this.branch,
  });

  final Key? key;

  final FloorModel? floor;

  final BranchItemsModel? branch;

  @override
  String toString() {
    return 'ListCustomerRouteArgs{key: $key, floor: $floor, branch: $branch}';
  }
}

/// generated route for
/// [ListInventoryPage]
class ListInventoryRoute extends PageRouteInfo<void> {
  const ListInventoryRoute({List<PageRouteInfo>? children})
      : super(
          ListInventoryRoute.name,
          initialChildren: children,
        );

  static const String name = 'ListInventoryRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ListInventoryPage();
    },
  );
}

/// generated route for
/// [LoginScreen]
class LoginRoute extends PageRouteInfo<void> {
  const LoginRoute({List<PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const LoginScreen();
    },
  );
}

/// generated route for
/// [MainPage]
class MainRoute extends PageRouteInfo<void> {
  const MainRoute({List<PageRouteInfo>? children})
      : super(
          MainRoute.name,
          initialChildren: children,
        );

  static const String name = 'MainRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MainPage();
    },
  );
}

/// generated route for
/// [MediaPage]
class MediaRoute extends PageRouteInfo<void> {
  const MediaRoute({List<PageRouteInfo>? children})
      : super(
          MediaRoute.name,
          initialChildren: children,
        );

  static const String name = 'MediaRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MediaPage();
    },
  );
}

/// generated route for
/// [MedicalDepartmentListPage]
class MedicalDepartmentListRoute
    extends PageRouteInfo<MedicalDepartmentListRouteArgs> {
  MedicalDepartmentListRoute({
    Key? key,
    required String? customerId,
    List<PageRouteInfo>? children,
  }) : super(
          MedicalDepartmentListRoute.name,
          args: MedicalDepartmentListRouteArgs(
            key: key,
            customerId: customerId,
          ),
          initialChildren: children,
        );

  static const String name = 'MedicalDepartmentListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MedicalDepartmentListRouteArgs>();
      return MedicalDepartmentListPage(
        key: args.key,
        customerId: args.customerId,
      );
    },
  );
}

class MedicalDepartmentListRouteArgs {
  const MedicalDepartmentListRouteArgs({
    this.key,
    required this.customerId,
  });

  final Key? key;

  final String? customerId;

  @override
  String toString() {
    return 'MedicalDepartmentListRouteArgs{key: $key, customerId: $customerId}';
  }
}

/// generated route for
/// [MedicalLogDetailPage]
class MedicalLogDetailRoute extends PageRouteInfo<MedicalLogDetailRouteArgs> {
  MedicalLogDetailRoute({
    Key? key,
    required MedicalServiceModel? medicalServiceModel,
    required MedicalServiceLogModel? medicalServiceLogModel,
    required MedicalDepartmentCode departmentCode,
    List<PageRouteInfo>? children,
  }) : super(
          MedicalLogDetailRoute.name,
          args: MedicalLogDetailRouteArgs(
            key: key,
            medicalServiceModel: medicalServiceModel,
            medicalServiceLogModel: medicalServiceLogModel,
            departmentCode: departmentCode,
          ),
          initialChildren: children,
        );

  static const String name = 'MedicalLogDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MedicalLogDetailRouteArgs>();
      return MedicalLogDetailPage(
        key: args.key,
        medicalServiceModel: args.medicalServiceModel,
        medicalServiceLogModel: args.medicalServiceLogModel,
        departmentCode: args.departmentCode,
      );
    },
  );
}

class MedicalLogDetailRouteArgs {
  const MedicalLogDetailRouteArgs({
    this.key,
    required this.medicalServiceModel,
    required this.medicalServiceLogModel,
    required this.departmentCode,
  });

  final Key? key;

  final MedicalServiceModel? medicalServiceModel;

  final MedicalServiceLogModel? medicalServiceLogModel;

  final MedicalDepartmentCode departmentCode;

  @override
  String toString() {
    return 'MedicalLogDetailRouteArgs{key: $key, medicalServiceModel: $medicalServiceModel, medicalServiceLogModel: $medicalServiceLogModel, departmentCode: $departmentCode}';
  }
}

/// generated route for
/// [MedicalProductCreationPage]
class MedicalProductCreationRoute
    extends PageRouteInfo<MedicalProductCreationRouteArgs> {
  MedicalProductCreationRoute({
    Key? key,
    required String? customerId,
    List<PageRouteInfo>? children,
  }) : super(
          MedicalProductCreationRoute.name,
          args: MedicalProductCreationRouteArgs(
            key: key,
            customerId: customerId,
          ),
          initialChildren: children,
        );

  static const String name = 'MedicalProductCreationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MedicalProductCreationRouteArgs>();
      return MedicalProductCreationPage(
        key: args.key,
        customerId: args.customerId,
      );
    },
  );
}

class MedicalProductCreationRouteArgs {
  const MedicalProductCreationRouteArgs({
    this.key,
    required this.customerId,
  });

  final Key? key;

  final String? customerId;

  @override
  String toString() {
    return 'MedicalProductCreationRouteArgs{key: $key, customerId: $customerId}';
  }
}

/// generated route for
/// [MedicalServiceCreationPage]
class MedicalServiceCreationRoute
    extends PageRouteInfo<MedicalServiceCreationRouteArgs> {
  MedicalServiceCreationRoute({
    Key? key,
    required String? customerId,
    List<PageRouteInfo>? children,
  }) : super(
          MedicalServiceCreationRoute.name,
          args: MedicalServiceCreationRouteArgs(
            key: key,
            customerId: customerId,
          ),
          initialChildren: children,
        );

  static const String name = 'MedicalServiceCreationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MedicalServiceCreationRouteArgs>();
      return MedicalServiceCreationPage(
        key: args.key,
        customerId: args.customerId,
      );
    },
  );
}

class MedicalServiceCreationRouteArgs {
  const MedicalServiceCreationRouteArgs({
    this.key,
    required this.customerId,
  });

  final Key? key;

  final String? customerId;

  @override
  String toString() {
    return 'MedicalServiceCreationRouteArgs{key: $key, customerId: $customerId}';
  }
}

/// generated route for
/// [MedicalServiceListPage]
class MedicalServiceListRoute
    extends PageRouteInfo<MedicalServiceListRouteArgs> {
  MedicalServiceListRoute({
    Key? key,
    required MedicalDepartmentModel? data,
    required String? customerId,
    List<PageRouteInfo>? children,
  }) : super(
          MedicalServiceListRoute.name,
          args: MedicalServiceListRouteArgs(
            key: key,
            data: data,
            customerId: customerId,
          ),
          initialChildren: children,
        );

  static const String name = 'MedicalServiceListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MedicalServiceListRouteArgs>();
      return MedicalServiceListPage(
        key: args.key,
        data: args.data,
        customerId: args.customerId,
      );
    },
  );
}

class MedicalServiceListRouteArgs {
  const MedicalServiceListRouteArgs({
    this.key,
    required this.data,
    required this.customerId,
  });

  final Key? key;

  final MedicalDepartmentModel? data;

  final String? customerId;

  @override
  String toString() {
    return 'MedicalServiceListRouteArgs{key: $key, data: $data, customerId: $customerId}';
  }
}

/// generated route for
/// [MedicalServiceLogListPage]
class MedicalServiceLogListRoute
    extends PageRouteInfo<MedicalServiceLogListRouteArgs> {
  MedicalServiceLogListRoute({
    Key? key,
    required MedicalServiceModel? service,
    required MedicalDepartmentCode departmentCode,
    List<PageRouteInfo>? children,
  }) : super(
          MedicalServiceLogListRoute.name,
          args: MedicalServiceLogListRouteArgs(
            key: key,
            service: service,
            departmentCode: departmentCode,
          ),
          initialChildren: children,
        );

  static const String name = 'MedicalServiceLogListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MedicalServiceLogListRouteArgs>();
      return MedicalServiceLogListPage(
        key: args.key,
        service: args.service,
        departmentCode: args.departmentCode,
      );
    },
  );
}

class MedicalServiceLogListRouteArgs {
  const MedicalServiceLogListRouteArgs({
    this.key,
    required this.service,
    required this.departmentCode,
  });

  final Key? key;

  final MedicalServiceModel? service;

  final MedicalDepartmentCode departmentCode;

  @override
  String toString() {
    return 'MedicalServiceLogListRouteArgs{key: $key, service: $service, departmentCode: $departmentCode}';
  }
}

/// generated route for
/// [MedicalTemplateListPage]
class MedicalTemplateListRoute extends PageRouteInfo<void> {
  const MedicalTemplateListRoute({List<PageRouteInfo>? children})
      : super(
          MedicalTemplateListRoute.name,
          initialChildren: children,
        );

  static const String name = 'MedicalTemplateListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MedicalTemplateListPage();
    },
  );
}

/// generated route for
/// [MedicineDetailPage]
class MedicineDetailRoute extends PageRouteInfo<MedicineDetailRouteArgs> {
  MedicineDetailRoute({
    Key? key,
    MedicineDetail? data,
    bool hasButton = true,
    List<PageRouteInfo>? children,
  }) : super(
          MedicineDetailRoute.name,
          args: MedicineDetailRouteArgs(
            key: key,
            data: data,
            hasButton: hasButton,
          ),
          initialChildren: children,
        );

  static const String name = 'MedicineDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MedicineDetailRouteArgs>(
          orElse: () => const MedicineDetailRouteArgs());
      return MedicineDetailPage(
        key: args.key,
        data: args.data,
        hasButton: args.hasButton,
      );
    },
  );
}

class MedicineDetailRouteArgs {
  const MedicineDetailRouteArgs({
    this.key,
    this.data,
    this.hasButton = true,
  });

  final Key? key;

  final MedicineDetail? data;

  final bool hasButton;

  @override
  String toString() {
    return 'MedicineDetailRouteArgs{key: $key, data: $data, hasButton: $hasButton}';
  }
}

/// generated route for
/// [MonthlyHistoryCheckinPage]
class MonthlyHistoryCheckinRoute
    extends PageRouteInfo<MonthlyHistoryCheckinRouteArgs> {
  MonthlyHistoryCheckinRoute({
    Key? key,
    required String month,
    required String year,
    List<PageRouteInfo>? children,
  }) : super(
          MonthlyHistoryCheckinRoute.name,
          args: MonthlyHistoryCheckinRouteArgs(
            key: key,
            month: month,
            year: year,
          ),
          rawPathParams: {
            'month': month,
            'year': year,
          },
          initialChildren: children,
        );

  static const String name = 'MonthlyHistoryCheckinRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<MonthlyHistoryCheckinRouteArgs>(
          orElse: () => MonthlyHistoryCheckinRouteArgs(
                month: pathParams.getString('month'),
                year: pathParams.getString('year'),
              ));
      return MonthlyHistoryCheckinPage(
        key: args.key,
        month: args.month,
        year: args.year,
      );
    },
  );
}

class MonthlyHistoryCheckinRouteArgs {
  const MonthlyHistoryCheckinRouteArgs({
    this.key,
    required this.month,
    required this.year,
  });

  final Key? key;

  final String month;

  final String year;

  @override
  String toString() {
    return 'MonthlyHistoryCheckinRouteArgs{key: $key, month: $month, year: $year}';
  }
}

/// generated route for
/// [MorePage]
class MoreRoute extends PageRouteInfo<void> {
  const MoreRoute({List<PageRouteInfo>? children})
      : super(
          MoreRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MorePage();
    },
  );
}

/// generated route for
/// [NDEventsPage]
class NDEventsRoute extends PageRouteInfo<void> {
  const NDEventsRoute({List<PageRouteInfo>? children})
      : super(
          NDEventsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NDEventsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const NDEventsPage();
    },
  );
}

/// generated route for
/// [NewsScreen]
class NewsRoute extends PageRouteInfo<void> {
  const NewsRoute({List<PageRouteInfo>? children})
      : super(
          NewsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NewsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const NewsScreen();
    },
  );
}

/// generated route for
/// [NoInternetPage]
class NoInternetRoute extends PageRouteInfo<void> {
  const NoInternetRoute({List<PageRouteInfo>? children})
      : super(
          NoInternetRoute.name,
          initialChildren: children,
        );

  static const String name = 'NoInternetRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const NoInternetPage();
    },
  );
}

/// generated route for
/// [NoteDetailsPage]
class NoteDetailsRoute extends PageRouteInfo<NoteDetailsRouteArgs> {
  NoteDetailsRoute({
    Key? key,
    required ImportantNoteModel? note,
    required String? customerID,
    required String? code,
    List<PageRouteInfo>? children,
  }) : super(
          NoteDetailsRoute.name,
          args: NoteDetailsRouteArgs(
            key: key,
            note: note,
            customerID: customerID,
            code: code,
          ),
          initialChildren: children,
        );

  static const String name = 'NoteDetailsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<NoteDetailsRouteArgs>();
      return NoteDetailsPage(
        key: args.key,
        note: args.note,
        customerID: args.customerID,
        code: args.code,
      );
    },
  );
}

class NoteDetailsRouteArgs {
  const NoteDetailsRouteArgs({
    this.key,
    required this.note,
    required this.customerID,
    required this.code,
  });

  final Key? key;

  final ImportantNoteModel? note;

  final String? customerID;

  final String? code;

  @override
  String toString() {
    return 'NoteDetailsRouteArgs{key: $key, note: $note, customerID: $customerID, code: $code}';
  }
}

/// generated route for
/// [NotificationListPage]
class NotificationListRoute extends PageRouteInfo<void> {
  const NotificationListRoute({List<PageRouteInfo>? children})
      : super(
          NotificationListRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const NotificationListPage();
    },
  );
}

/// generated route for
/// [NotificationsScreen]
class NotificationsRoute extends PageRouteInfo<void> {
  const NotificationsRoute({List<PageRouteInfo>? children})
      : super(
          NotificationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const NotificationsScreen();
    },
  );
}

/// generated route for
/// [OnBoardingScreen]
class OnBoardingRoute extends PageRouteInfo<void> {
  const OnBoardingRoute({List<PageRouteInfo>? children})
      : super(
          OnBoardingRoute.name,
          initialChildren: children,
        );

  static const String name = 'OnBoardingRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const OnBoardingScreen();
    },
  );
}

/// generated route for
/// [OptionLoginScreen]
class OptionLoginRoute extends PageRouteInfo<void> {
  const OptionLoginRoute({List<PageRouteInfo>? children})
      : super(
          OptionLoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'OptionLoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const OptionLoginScreen();
    },
  );
}

/// generated route for
/// [OrderFoodComplaintPage]
class OrderFoodComplaintRoute extends PageRouteInfo<void> {
  const OrderFoodComplaintRoute({List<PageRouteInfo>? children})
      : super(
          OrderFoodComplaintRoute.name,
          initialChildren: children,
        );

  static const String name = 'OrderFoodComplaintRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const OrderFoodComplaintPage();
    },
  );
}

/// generated route for
/// [OrderFoodPage]
class OrderFoodRoute extends PageRouteInfo<OrderFoodRouteArgs> {
  OrderFoodRoute({
    int initialTabIndex = 0,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          OrderFoodRoute.name,
          args: OrderFoodRouteArgs(
            initialTabIndex: initialTabIndex,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'OrderFoodRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<OrderFoodRouteArgs>(
          orElse: () => const OrderFoodRouteArgs());
      return OrderFoodPage(
        initialTabIndex: args.initialTabIndex,
        key: args.key,
      );
    },
  );
}

class OrderFoodRouteArgs {
  const OrderFoodRouteArgs({
    this.initialTabIndex = 0,
    this.key,
  });

  final int initialTabIndex;

  final Key? key;

  @override
  String toString() {
    return 'OrderFoodRouteArgs{initialTabIndex: $initialTabIndex, key: $key}';
  }
}

/// generated route for
/// [OrderFoodRegulationsPage]
class OrderFoodRegulationsRoute extends PageRouteInfo<void> {
  const OrderFoodRegulationsRoute({List<PageRouteInfo>? children})
      : super(
          OrderFoodRegulationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'OrderFoodRegulationsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const OrderFoodRegulationsPage();
    },
  );
}

/// generated route for
/// [ProductConfirmPage]
class ProductConfirmRoute extends PageRouteInfo<ProductConfirmRouteArgs> {
  ProductConfirmRoute({
    Key? key,
    required String organizationId,
    List<PageRouteInfo>? children,
  }) : super(
          ProductConfirmRoute.name,
          args: ProductConfirmRouteArgs(
            key: key,
            organizationId: organizationId,
          ),
          rawPathParams: {'organizationId': organizationId},
          initialChildren: children,
        );

  static const String name = 'ProductConfirmRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<ProductConfirmRouteArgs>(
          orElse: () => ProductConfirmRouteArgs(
              organizationId: pathParams.getString('organizationId')));
      return ProductConfirmPage(
        key: args.key,
        organizationId: args.organizationId,
      );
    },
  );
}

class ProductConfirmRouteArgs {
  const ProductConfirmRouteArgs({
    this.key,
    required this.organizationId,
  });

  final Key? key;

  final String organizationId;

  @override
  String toString() {
    return 'ProductConfirmRouteArgs{key: $key, organizationId: $organizationId}';
  }
}

/// generated route for
/// [ProductDetailConfirmPage]
class ProductDetailConfirmRoute
    extends PageRouteInfo<ProductDetailConfirmRouteArgs> {
  ProductDetailConfirmRoute({
    Key? key,
    required String organizationId,
    required String id,
    List<PageRouteInfo>? children,
  }) : super(
          ProductDetailConfirmRoute.name,
          args: ProductDetailConfirmRouteArgs(
            key: key,
            organizationId: organizationId,
            id: id,
          ),
          rawPathParams: {
            'organizationId': organizationId,
            'id': id,
          },
          initialChildren: children,
        );

  static const String name = 'ProductDetailConfirmRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<ProductDetailConfirmRouteArgs>(
          orElse: () => ProductDetailConfirmRouteArgs(
                organizationId: pathParams.getString('organizationId'),
                id: pathParams.getString('id'),
              ));
      return ProductDetailConfirmPage(
        key: args.key,
        organizationId: args.organizationId,
        id: args.id,
      );
    },
  );
}

class ProductDetailConfirmRouteArgs {
  const ProductDetailConfirmRouteArgs({
    this.key,
    required this.organizationId,
    required this.id,
  });

  final Key? key;

  final String organizationId;

  final String id;

  @override
  String toString() {
    return 'ProductDetailConfirmRouteArgs{key: $key, organizationId: $organizationId, id: $id}';
  }
}

/// generated route for
/// [ProfileGalleryPage]
class ProfileGalleryRoute extends PageRouteInfo<ProfileGalleryRouteArgs> {
  ProfileGalleryRoute({
    Key? key,
    FromPage isFromProfile = FromPage.profile,
    bool isMultiple = false,
    bool isUpdateBackground = false,
    List<PageRouteInfo>? children,
  }) : super(
          ProfileGalleryRoute.name,
          args: ProfileGalleryRouteArgs(
            key: key,
            isFromProfile: isFromProfile,
            isMultiple: isMultiple,
            isUpdateBackground: isUpdateBackground,
          ),
          initialChildren: children,
        );

  static const String name = 'ProfileGalleryRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ProfileGalleryRouteArgs>(
          orElse: () => const ProfileGalleryRouteArgs());
      return ProfileGalleryPage(
        key: args.key,
        isFromProfile: args.isFromProfile,
        isMultiple: args.isMultiple,
        isUpdateBackground: args.isUpdateBackground,
      );
    },
  );
}

class ProfileGalleryRouteArgs {
  const ProfileGalleryRouteArgs({
    this.key,
    this.isFromProfile = FromPage.profile,
    this.isMultiple = false,
    this.isUpdateBackground = false,
  });

  final Key? key;

  final FromPage isFromProfile;

  final bool isMultiple;

  final bool isUpdateBackground;

  @override
  String toString() {
    return 'ProfileGalleryRouteArgs{key: $key, isFromProfile: $isFromProfile, isMultiple: $isMultiple, isUpdateBackground: $isUpdateBackground}';
  }
}

/// generated route for
/// [ProfilePage]
class ProfileRoute extends PageRouteInfo<ProfileRouteArgs> {
  ProfileRoute({
    Key? key,
    UserBundleModel? info,
    List<PageRouteInfo>? children,
  }) : super(
          ProfileRoute.name,
          args: ProfileRouteArgs(
            key: key,
            info: info,
          ),
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<ProfileRouteArgs>(orElse: () => const ProfileRouteArgs());
      return ProfilePage(
        key: args.key,
        info: args.info,
      );
    },
  );
}

class ProfileRouteArgs {
  const ProfileRouteArgs({
    this.key,
    this.info,
  });

  final Key? key;

  final UserBundleModel? info;

  @override
  String toString() {
    return 'ProfileRouteArgs{key: $key, info: $info}';
  }
}

/// generated route for
/// [ProfilePreviewImagePage]
class ProfilePreviewImageRoute
    extends PageRouteInfo<ProfilePreviewImageRouteArgs> {
  ProfilePreviewImageRoute({
    Key? key,
    required String path,
    bool background = false,
    List<PageRouteInfo>? children,
  }) : super(
          ProfilePreviewImageRoute.name,
          args: ProfilePreviewImageRouteArgs(
            key: key,
            path: path,
            background: background,
          ),
          initialChildren: children,
        );

  static const String name = 'ProfilePreviewImageRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ProfilePreviewImageRouteArgs>();
      return ProfilePreviewImagePage(
        key: args.key,
        path: args.path,
        background: args.background,
      );
    },
  );
}

class ProfilePreviewImageRouteArgs {
  const ProfilePreviewImageRouteArgs({
    this.key,
    required this.path,
    this.background = false,
  });

  final Key? key;

  final String path;

  final bool background;

  @override
  String toString() {
    return 'ProfilePreviewImageRouteArgs{key: $key, path: $path, background: $background}';
  }
}

/// generated route for
/// [PxListPage]
class PxListRoute extends PageRouteInfo<PxListRouteArgs> {
  PxListRoute({
    Key? key,
    required String? lsLevel1Id,
    required String? roomCode,
    List<PageRouteInfo>? children,
  }) : super(
          PxListRoute.name,
          args: PxListRouteArgs(
            key: key,
            lsLevel1Id: lsLevel1Id,
            roomCode: roomCode,
          ),
          rawQueryParams: {
            'lsLevel1Id': lsLevel1Id,
            'roomCode': roomCode,
          },
          initialChildren: children,
        );

  static const String name = 'PxListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final queryParams = data.queryParams;
      final args = data.argsAs<PxListRouteArgs>(
          orElse: () => PxListRouteArgs(
                lsLevel1Id: queryParams.optString('lsLevel1Id'),
                roomCode: queryParams.optString('roomCode'),
              ));
      return PxListPage(
        key: args.key,
        lsLevel1Id: args.lsLevel1Id,
        roomCode: args.roomCode,
      );
    },
  );
}

class PxListRouteArgs {
  const PxListRouteArgs({
    this.key,
    required this.lsLevel1Id,
    required this.roomCode,
  });

  final Key? key;

  final String? lsLevel1Id;

  final String? roomCode;

  @override
  String toString() {
    return 'PxListRouteArgs{key: $key, lsLevel1Id: $lsLevel1Id, roomCode: $roomCode}';
  }
}

/// generated route for
/// [PxTaskListPage]
class PxTaskListRoute extends PageRouteInfo<PxTaskListRouteArgs> {
  PxTaskListRoute({
    required String? roomCode,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          PxTaskListRoute.name,
          args: PxTaskListRouteArgs(
            roomCode: roomCode,
            key: key,
          ),
          rawPathParams: {'roomCode': roomCode},
          initialChildren: children,
        );

  static const String name = 'PxTaskListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<PxTaskListRouteArgs>(
          orElse: () =>
              PxTaskListRouteArgs(roomCode: pathParams.optString('roomCode')));
      return PxTaskListPage(
        roomCode: args.roomCode,
        key: args.key,
      );
    },
  );
}

class PxTaskListRouteArgs {
  const PxTaskListRouteArgs({
    required this.roomCode,
    this.key,
  });

  final String? roomCode;

  final Key? key;

  @override
  String toString() {
    return 'PxTaskListRouteArgs{roomCode: $roomCode, key: $key}';
  }
}

/// generated route for
/// [RatingHumanPage]
class RatingHumanRoute extends PageRouteInfo<RatingHumanRouteArgs> {
  RatingHumanRoute({
    Key? key,
    String? employeeId,
    String? employeeName,
    String? positionId,
    String? positionName,
    String? avatar,
    String? appraisalPeriodId,
    String? objectAppraisalId,
    String? employeeAppraisalId,
    List<PageRouteInfo>? children,
  }) : super(
          RatingHumanRoute.name,
          args: RatingHumanRouteArgs(
            key: key,
            employeeId: employeeId,
            employeeName: employeeName,
            positionId: positionId,
            positionName: positionName,
            avatar: avatar,
            appraisalPeriodId: appraisalPeriodId,
            objectAppraisalId: objectAppraisalId,
            employeeAppraisalId: employeeAppraisalId,
          ),
          initialChildren: children,
        );

  static const String name = 'RatingHumanRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<RatingHumanRouteArgs>(
          orElse: () => const RatingHumanRouteArgs());
      return RatingHumanPage(
        key: args.key,
        employeeId: args.employeeId,
        employeeName: args.employeeName,
        positionId: args.positionId,
        positionName: args.positionName,
        avatar: args.avatar,
        appraisalPeriodId: args.appraisalPeriodId,
        objectAppraisalId: args.objectAppraisalId,
        employeeAppraisalId: args.employeeAppraisalId,
      );
    },
  );
}

class RatingHumanRouteArgs {
  const RatingHumanRouteArgs({
    this.key,
    this.employeeId,
    this.employeeName,
    this.positionId,
    this.positionName,
    this.avatar,
    this.appraisalPeriodId,
    this.objectAppraisalId,
    this.employeeAppraisalId,
  });

  final Key? key;

  final String? employeeId;

  final String? employeeName;

  final String? positionId;

  final String? positionName;

  final String? avatar;

  final String? appraisalPeriodId;

  final String? objectAppraisalId;

  final String? employeeAppraisalId;

  @override
  String toString() {
    return 'RatingHumanRouteArgs{key: $key, employeeId: $employeeId, employeeName: $employeeName, positionId: $positionId, positionName: $positionName, avatar: $avatar, appraisalPeriodId: $appraisalPeriodId, objectAppraisalId: $objectAppraisalId, employeeAppraisalId: $employeeAppraisalId}';
  }
}

/// generated route for
/// [ScheduleDetailsPage]
class ScheduleDetailsRoute extends PageRouteInfo<ScheduleDetailsRouteArgs> {
  ScheduleDetailsRoute({
    required String customerID,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ScheduleDetailsRoute.name,
          args: ScheduleDetailsRouteArgs(
            customerID: customerID,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ScheduleDetailsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ScheduleDetailsRouteArgs>();
      return ScheduleDetailsPage(
        args.customerID,
        key: args.key,
      );
    },
  );
}

class ScheduleDetailsRouteArgs {
  const ScheduleDetailsRouteArgs({
    required this.customerID,
    this.key,
  });

  final String customerID;

  final Key? key;

  @override
  String toString() {
    return 'ScheduleDetailsRouteArgs{customerID: $customerID, key: $key}';
  }
}

/// generated route for
/// [SelectPxRoomPage]
class SelectPxRoomRoute extends PageRouteInfo<SelectPxRoomRouteArgs> {
  SelectPxRoomRoute({
    Key? key,
    required String? lsLevel1ID,
    required PxCustomer? customer,
    List<PageRouteInfo>? children,
  }) : super(
          SelectPxRoomRoute.name,
          args: SelectPxRoomRouteArgs(
            key: key,
            lsLevel1ID: lsLevel1ID,
            customer: customer,
          ),
          initialChildren: children,
        );

  static const String name = 'SelectPxRoomRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelectPxRoomRouteArgs>();
      return SelectPxRoomPage(
        key: args.key,
        lsLevel1ID: args.lsLevel1ID,
        customer: args.customer,
      );
    },
  );
}

class SelectPxRoomRouteArgs {
  const SelectPxRoomRouteArgs({
    this.key,
    required this.lsLevel1ID,
    required this.customer,
  });

  final Key? key;

  final String? lsLevel1ID;

  final PxCustomer? customer;

  @override
  String toString() {
    return 'SelectPxRoomRouteArgs{key: $key, lsLevel1ID: $lsLevel1ID, customer: $customer}';
  }
}

/// generated route for
/// [SelectedAssigneeScreen]
class SelectedAssigneeRoute extends PageRouteInfo<SelectedAssigneeRouteArgs> {
  SelectedAssigneeRoute({
    Key? key,
    required List<JobSchedulerStaffModel>? assignees,
    List<PageRouteInfo>? children,
  }) : super(
          SelectedAssigneeRoute.name,
          args: SelectedAssigneeRouteArgs(
            key: key,
            assignees: assignees,
          ),
          initialChildren: children,
        );

  static const String name = 'SelectedAssigneeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelectedAssigneeRouteArgs>();
      return SelectedAssigneeScreen(
        key: args.key,
        assignees: args.assignees,
      );
    },
  );
}

class SelectedAssigneeRouteArgs {
  const SelectedAssigneeRouteArgs({
    this.key,
    required this.assignees,
  });

  final Key? key;

  final List<JobSchedulerStaffModel>? assignees;

  @override
  String toString() {
    return 'SelectedAssigneeRouteArgs{key: $key, assignees: $assignees}';
  }
}

/// generated route for
/// [SelectingOfficePage]
class SelectingOfficeRoute extends PageRouteInfo<SelectingOfficeRouteArgs> {
  SelectingOfficeRoute({
    Key? key,
    List<JobSchedulerStaffModel?>? staffs,
    List<PageRouteInfo>? children,
  }) : super(
          SelectingOfficeRoute.name,
          args: SelectingOfficeRouteArgs(
            key: key,
            staffs: staffs,
          ),
          initialChildren: children,
        );

  static const String name = 'SelectingOfficeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelectingOfficeRouteArgs>(
          orElse: () => const SelectingOfficeRouteArgs());
      return SelectingOfficePage(
        key: args.key,
        staffs: args.staffs,
      );
    },
  );
}

class SelectingOfficeRouteArgs {
  const SelectingOfficeRouteArgs({
    this.key,
    this.staffs,
  });

  final Key? key;

  final List<JobSchedulerStaffModel?>? staffs;

  @override
  String toString() {
    return 'SelectingOfficeRouteArgs{key: $key, staffs: $staffs}';
  }
}

/// generated route for
/// [SelectingStaffPage]
class SelectingStaffRoute extends PageRouteInfo<SelectingStaffRouteArgs> {
  SelectingStaffRoute({
    Key? key,
    required SelectingStaffScreenParams selectingStaffScreenParams,
    List<PageRouteInfo>? children,
  }) : super(
          SelectingStaffRoute.name,
          args: SelectingStaffRouteArgs(
            key: key,
            selectingStaffScreenParams: selectingStaffScreenParams,
          ),
          initialChildren: children,
        );

  static const String name = 'SelectingStaffRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelectingStaffRouteArgs>();
      return SelectingStaffPage(
        key: args.key,
        selectingStaffScreenParams: args.selectingStaffScreenParams,
      );
    },
  );
}

class SelectingStaffRouteArgs {
  const SelectingStaffRouteArgs({
    this.key,
    required this.selectingStaffScreenParams,
  });

  final Key? key;

  final SelectingStaffScreenParams selectingStaffScreenParams;

  @override
  String toString() {
    return 'SelectingStaffRouteArgs{key: $key, selectingStaffScreenParams: $selectingStaffScreenParams}';
  }
}

/// generated route for
/// [ServiceAndProductPage]
class ServiceAndProductRoute extends PageRouteInfo<ServiceAndProductRouteArgs> {
  ServiceAndProductRoute({
    required String? customerID,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ServiceAndProductRoute.name,
          args: ServiceAndProductRouteArgs(
            customerID: customerID,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ServiceAndProductRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ServiceAndProductRouteArgs>();
      return ServiceAndProductPage(
        args.customerID,
        key: args.key,
      );
    },
  );
}

class ServiceAndProductRouteArgs {
  const ServiceAndProductRouteArgs({
    required this.customerID,
    this.key,
  });

  final String? customerID;

  final Key? key;

  @override
  String toString() {
    return 'ServiceAndProductRouteArgs{customerID: $customerID, key: $key}';
  }
}

/// generated route for
/// [SetPasswordScreen]
class SetPasswordRoute extends PageRouteInfo<SetPasswordRouteArgs> {
  SetPasswordRoute({
    Key? key,
    required OTP otp,
    List<PageRouteInfo>? children,
  }) : super(
          SetPasswordRoute.name,
          args: SetPasswordRouteArgs(
            key: key,
            otp: otp,
          ),
          initialChildren: children,
        );

  static const String name = 'SetPasswordRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SetPasswordRouteArgs>();
      return SetPasswordScreen(
        key: args.key,
        otp: args.otp,
      );
    },
  );
}

class SetPasswordRouteArgs {
  const SetPasswordRouteArgs({
    this.key,
    required this.otp,
  });

  final Key? key;

  final OTP otp;

  @override
  String toString() {
    return 'SetPasswordRouteArgs{key: $key, otp: $otp}';
  }
}

/// generated route for
/// [SettingScreen]
class SettingRoute extends PageRouteInfo<SettingRouteArgs> {
  SettingRoute({
    Key? key,
    bool isShowKYC = false,
    List<PageRouteInfo>? children,
  }) : super(
          SettingRoute.name,
          args: SettingRouteArgs(
            key: key,
            isShowKYC: isShowKYC,
          ),
          initialChildren: children,
        );

  static const String name = 'SettingRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<SettingRouteArgs>(orElse: () => const SettingRouteArgs());
      return SettingScreen(
        key: args.key,
        isShowKYC: args.isShowKYC,
      );
    },
  );
}

class SettingRouteArgs {
  const SettingRouteArgs({
    this.key,
    this.isShowKYC = false,
  });

  final Key? key;

  final bool isShowKYC;

  @override
  String toString() {
    return 'SettingRouteArgs{key: $key, isShowKYC: $isShowKYC}';
  }
}

/// generated route for
/// [SettingsScreen]
class SettingsRoute extends PageRouteInfo<void> {
  const SettingsRoute({List<PageRouteInfo>? children})
      : super(
          SettingsRoute.name,
          initialChildren: children,
        );

  static const String name = 'SettingsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SettingsScreen();
    },
  );
}

/// generated route for
/// [SplashScreen]
class SplashRoute extends PageRouteInfo<void> {
  const SplashRoute({List<PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SplashScreen();
    },
  );
}

/// generated route for
/// [StaffEvaluationPeriodsPage]
class StaffEvaluationPeriodsRoute
    extends PageRouteInfo<StaffEvaluationPeriodsRouteArgs> {
  StaffEvaluationPeriodsRoute({
    Key? key,
    required String? organizationId,
    List<PageRouteInfo>? children,
  }) : super(
          StaffEvaluationPeriodsRoute.name,
          args: StaffEvaluationPeriodsRouteArgs(
            key: key,
            organizationId: organizationId,
          ),
          rawPathParams: {'organizationId': organizationId},
          initialChildren: children,
        );

  static const String name = 'StaffEvaluationPeriodsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<StaffEvaluationPeriodsRouteArgs>(
          orElse: () => StaffEvaluationPeriodsRouteArgs(
              organizationId: pathParams.optString('organizationId')));
      return StaffEvaluationPeriodsPage(
        key: args.key,
        organizationId: args.organizationId,
      );
    },
  );
}

class StaffEvaluationPeriodsRouteArgs {
  const StaffEvaluationPeriodsRouteArgs({
    this.key,
    required this.organizationId,
  });

  final Key? key;

  final String? organizationId;

  @override
  String toString() {
    return 'StaffEvaluationPeriodsRouteArgs{key: $key, organizationId: $organizationId}';
  }
}

/// generated route for
/// [StoryDetailPage]
class StoryDetailRoute extends PageRouteInfo<StoryDetailRouteArgs> {
  StoryDetailRoute({
    Key? key,
    required String id,
    bool fromNotification = false,
    String commentId = '',
    String? type,
    List<PageRouteInfo>? children,
  }) : super(
          StoryDetailRoute.name,
          args: StoryDetailRouteArgs(
            key: key,
            id: id,
            fromNotification: fromNotification,
            commentId: commentId,
            type: type,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'StoryDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<StoryDetailRouteArgs>(
          orElse: () => StoryDetailRouteArgs(id: pathParams.getString('id')));
      return StoryDetailPage(
        key: args.key,
        id: args.id,
        fromNotification: args.fromNotification,
        commentId: args.commentId,
        type: args.type,
      );
    },
  );
}

class StoryDetailRouteArgs {
  const StoryDetailRouteArgs({
    this.key,
    required this.id,
    this.fromNotification = false,
    this.commentId = '',
    this.type,
  });

  final Key? key;

  final String id;

  final bool fromNotification;

  final String commentId;

  final String? type;

  @override
  String toString() {
    return 'StoryDetailRouteArgs{key: $key, id: $id, fromNotification: $fromNotification, commentId: $commentId, type: $type}';
  }
}

/// generated route for
/// [StoryEditImagePage]
class StoryEditImageRoute extends PageRouteInfo<StoryEditImageRouteArgs> {
  StoryEditImageRoute({
    Key? key,
    required File file,
    required int i,
    bool isUpdate = false,
    List<PageRouteInfo>? children,
  }) : super(
          StoryEditImageRoute.name,
          args: StoryEditImageRouteArgs(
            key: key,
            file: file,
            i: i,
            isUpdate: isUpdate,
          ),
          initialChildren: children,
        );

  static const String name = 'StoryEditImageRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoryEditImageRouteArgs>();
      return StoryEditImagePage(
        key: args.key,
        file: args.file,
        i: args.i,
        isUpdate: args.isUpdate,
      );
    },
  );
}

class StoryEditImageRouteArgs {
  const StoryEditImageRouteArgs({
    this.key,
    required this.file,
    required this.i,
    this.isUpdate = false,
  });

  final Key? key;

  final File file;

  final int i;

  final bool isUpdate;

  @override
  String toString() {
    return 'StoryEditImageRouteArgs{key: $key, file: $file, i: $i, isUpdate: $isUpdate}';
  }
}

/// generated route for
/// [StoryImageDetailPage]
class StoryImageDetailRoute extends PageRouteInfo<StoryImageDetailRouteArgs> {
  StoryImageDetailRoute({
    Key? key,
    Attachment? file,
    required int initIndex,
    List<String>? tags,
    List<String>? url,
    List<String>? fileName,
    List<PageRouteInfo>? children,
  }) : super(
          StoryImageDetailRoute.name,
          args: StoryImageDetailRouteArgs(
            key: key,
            file: file,
            initIndex: initIndex,
            tags: tags,
            url: url,
            fileName: fileName,
          ),
          initialChildren: children,
        );

  static const String name = 'StoryImageDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoryImageDetailRouteArgs>();
      return StoryImageDetailPage(
        key: args.key,
        file: args.file,
        initIndex: args.initIndex,
        tags: args.tags,
        url: args.url,
        fileName: args.fileName,
      );
    },
  );
}

class StoryImageDetailRouteArgs {
  const StoryImageDetailRouteArgs({
    this.key,
    this.file,
    required this.initIndex,
    this.tags,
    this.url,
    this.fileName,
  });

  final Key? key;

  final Attachment? file;

  final int initIndex;

  final List<String>? tags;

  final List<String>? url;

  final List<String>? fileName;

  @override
  String toString() {
    return 'StoryImageDetailRouteArgs{key: $key, file: $file, initIndex: $initIndex, tags: $tags, url: $url, fileName: $fileName}';
  }
}

/// generated route for
/// [StoryPersonPage]
class StoryPersonRoute extends PageRouteInfo<StoryPersonRouteArgs> {
  StoryPersonRoute({
    Key? key,
    required String codeUser,
    List<PageRouteInfo>? children,
  }) : super(
          StoryPersonRoute.name,
          args: StoryPersonRouteArgs(
            key: key,
            codeUser: codeUser,
          ),
          initialChildren: children,
        );

  static const String name = 'StoryPersonRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoryPersonRouteArgs>();
      return StoryPersonPage(
        key: args.key,
        codeUser: args.codeUser,
      );
    },
  );
}

class StoryPersonRouteArgs {
  const StoryPersonRouteArgs({
    this.key,
    required this.codeUser,
  });

  final Key? key;

  final String codeUser;

  @override
  String toString() {
    return 'StoryPersonRouteArgs{key: $key, codeUser: $codeUser}';
  }
}

/// generated route for
/// [StorySearchPage]
class StorySearchRoute extends PageRouteInfo<StorySearchRouteArgs> {
  StorySearchRoute({
    Key? key,
    required String text,
    List<PageRouteInfo>? children,
  }) : super(
          StorySearchRoute.name,
          args: StorySearchRouteArgs(
            key: key,
            text: text,
          ),
          rawPathParams: {'text': text},
          initialChildren: children,
        );

  static const String name = 'StorySearchRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<StorySearchRouteArgs>(
          orElse: () =>
              StorySearchRouteArgs(text: pathParams.getString('text')));
      return StorySearchPage(
        key: args.key,
        text: args.text,
      );
    },
  );
}

class StorySearchRouteArgs {
  const StorySearchRouteArgs({
    this.key,
    required this.text,
  });

  final Key? key;

  final String text;

  @override
  String toString() {
    return 'StorySearchRouteArgs{key: $key, text: $text}';
  }
}

/// generated route for
/// [StoryVideoDetailPage]
class StoryVideoDetailRoute extends PageRouteInfo<StoryVideoDetailRouteArgs> {
  StoryVideoDetailRoute({
    Key? key,
    required VideoPlayerController play2,
    required String tag,
    required Duration duration,
    required String thumbnail,
    required String url,
    required String fileName,
    required int originalWidth,
    required int originalHeight,
    required ValueNotifier<PlayerType?> isLoadingWhenPlaying,
    List<PageRouteInfo>? children,
  }) : super(
          StoryVideoDetailRoute.name,
          args: StoryVideoDetailRouteArgs(
            key: key,
            play2: play2,
            tag: tag,
            duration: duration,
            thumbnail: thumbnail,
            url: url,
            fileName: fileName,
            originalWidth: originalWidth,
            originalHeight: originalHeight,
            isLoadingWhenPlaying: isLoadingWhenPlaying,
          ),
          initialChildren: children,
        );

  static const String name = 'StoryVideoDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoryVideoDetailRouteArgs>();
      return StoryVideoDetailPage(
        key: args.key,
        play2: args.play2,
        tag: args.tag,
        duration: args.duration,
        thumbnail: args.thumbnail,
        url: args.url,
        fileName: args.fileName,
        originalWidth: args.originalWidth,
        originalHeight: args.originalHeight,
        isLoadingWhenPlaying: args.isLoadingWhenPlaying,
      );
    },
  );
}

class StoryVideoDetailRouteArgs {
  const StoryVideoDetailRouteArgs({
    this.key,
    required this.play2,
    required this.tag,
    required this.duration,
    required this.thumbnail,
    required this.url,
    required this.fileName,
    required this.originalWidth,
    required this.originalHeight,
    required this.isLoadingWhenPlaying,
  });

  final Key? key;

  final VideoPlayerController play2;

  final String tag;

  final Duration duration;

  final String thumbnail;

  final String url;

  final String fileName;

  final int originalWidth;

  final int originalHeight;

  final ValueNotifier<PlayerType?> isLoadingWhenPlaying;

  @override
  String toString() {
    return 'StoryVideoDetailRouteArgs{key: $key, play2: $play2, tag: $tag, duration: $duration, thumbnail: $thumbnail, url: $url, fileName: $fileName, originalWidth: $originalWidth, originalHeight: $originalHeight, isLoadingWhenPlaying: $isLoadingWhenPlaying}';
  }
}

/// generated route for
/// [StoryVideoFullPage]
class StoryVideoFullRoute extends PageRouteInfo<StoryVideoFullRouteArgs> {
  StoryVideoFullRoute({
    Key? key,
    required VideoPlayerController play2,
    required String tag,
    required Duration duration,
    required String thumbnail,
    required String url,
    required String fileName,
    List<PageRouteInfo>? children,
  }) : super(
          StoryVideoFullRoute.name,
          args: StoryVideoFullRouteArgs(
            key: key,
            play2: play2,
            tag: tag,
            duration: duration,
            thumbnail: thumbnail,
            url: url,
            fileName: fileName,
          ),
          initialChildren: children,
        );

  static const String name = 'StoryVideoFullRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoryVideoFullRouteArgs>();
      return StoryVideoFullPage(
        key: args.key,
        play2: args.play2,
        tag: args.tag,
        duration: args.duration,
        thumbnail: args.thumbnail,
        url: args.url,
        fileName: args.fileName,
      );
    },
  );
}

class StoryVideoFullRouteArgs {
  const StoryVideoFullRouteArgs({
    this.key,
    required this.play2,
    required this.tag,
    required this.duration,
    required this.thumbnail,
    required this.url,
    required this.fileName,
  });

  final Key? key;

  final VideoPlayerController play2;

  final String tag;

  final Duration duration;

  final String thumbnail;

  final String url;

  final String fileName;

  @override
  String toString() {
    return 'StoryVideoFullRouteArgs{key: $key, play2: $play2, tag: $tag, duration: $duration, thumbnail: $thumbnail, url: $url, fileName: $fileName}';
  }
}

/// generated route for
/// [StoryWritePage]
class StoryWriteRoute extends PageRouteInfo<StoryWriteRouteArgs> {
  StoryWriteRoute({
    Key? key,
    bool isUpdate = false,
    List<PageRouteInfo>? children,
  }) : super(
          StoryWriteRoute.name,
          args: StoryWriteRouteArgs(
            key: key,
            isUpdate: isUpdate,
          ),
          initialChildren: children,
        );

  static const String name = 'StoryWriteRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoryWriteRouteArgs>(
          orElse: () => const StoryWriteRouteArgs());
      return StoryWritePage(
        key: args.key,
        isUpdate: args.isUpdate,
      );
    },
  );
}

class StoryWriteRouteArgs {
  const StoryWriteRouteArgs({
    this.key,
    this.isUpdate = false,
  });

  final Key? key;

  final bool isUpdate;

  @override
  String toString() {
    return 'StoryWriteRouteArgs{key: $key, isUpdate: $isUpdate}';
  }
}

/// generated route for
/// [SupportRequestsScreen]
class SupportRequestsRoute extends PageRouteInfo<void> {
  const SupportRequestsRoute({List<PageRouteInfo>? children})
      : super(
          SupportRequestsRoute.name,
          initialChildren: children,
        );

  static const String name = 'SupportRequestsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SupportRequestsScreen();
    },
  );
}

/// generated route for
/// [TabBarPage]
class TabBarRoute extends PageRouteInfo<void> {
  const TabBarRoute({List<PageRouteInfo>? children})
      : super(
          TabBarRoute.name,
          initialChildren: children,
        );

  static const String name = 'TabBarRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const TabBarPage();
    },
  );
}

/// generated route for
/// [TagByResultImageListPage]
class TagByResultImageListRoute
    extends PageRouteInfo<TagByResultImageListRouteArgs> {
  TagByResultImageListRoute({
    Key? key,
    required List<String> tags,
    required List<CustomerGetRoomListItem> rooms,
    List<PageRouteInfo>? children,
  }) : super(
          TagByResultImageListRoute.name,
          args: TagByResultImageListRouteArgs(
            key: key,
            tags: tags,
            rooms: rooms,
          ),
          initialChildren: children,
        );

  static const String name = 'TagByResultImageListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TagByResultImageListRouteArgs>();
      return TagByResultImageListPage(
        key: args.key,
        tags: args.tags,
        rooms: args.rooms,
      );
    },
  );
}

class TagByResultImageListRouteArgs {
  const TagByResultImageListRouteArgs({
    this.key,
    required this.tags,
    required this.rooms,
  });

  final Key? key;

  final List<String> tags;

  final List<CustomerGetRoomListItem> rooms;

  @override
  String toString() {
    return 'TagByResultImageListRouteArgs{key: $key, tags: $tags, rooms: $rooms}';
  }
}

/// generated route for
/// [TagImagePage]
class TagImageRoute extends PageRouteInfo<void> {
  const TagImageRoute({List<PageRouteInfo>? children})
      : super(
          TagImageRoute.name,
          initialChildren: children,
        );

  static const String name = 'TagImageRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const TagImagePage();
    },
  );
}

/// generated route for
/// [TagImageSearchPage]
class TagImageSearchRoute extends PageRouteInfo<TagImageSearchRouteArgs> {
  TagImageSearchRoute({
    Key? key,
    required List<CustomerGetRoomListItem> rooms,
    required bool isFromTagImagePage,
    List<PageRouteInfo>? children,
  }) : super(
          TagImageSearchRoute.name,
          args: TagImageSearchRouteArgs(
            key: key,
            rooms: rooms,
            isFromTagImagePage: isFromTagImagePage,
          ),
          initialChildren: children,
        );

  static const String name = 'TagImageSearchRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TagImageSearchRouteArgs>();
      return TagImageSearchPage(
        key: args.key,
        rooms: args.rooms,
        isFromTagImagePage: args.isFromTagImagePage,
      );
    },
  );
}

class TagImageSearchRouteArgs {
  const TagImageSearchRouteArgs({
    this.key,
    required this.rooms,
    required this.isFromTagImagePage,
  });

  final Key? key;

  final List<CustomerGetRoomListItem> rooms;

  final bool isFromTagImagePage;

  @override
  String toString() {
    return 'TagImageSearchRouteArgs{key: $key, rooms: $rooms, isFromTagImagePage: $isFromTagImagePage}';
  }
}

/// generated route for
/// [TakingCareCustomerPage]
class TakingCareCustomerRoute
    extends PageRouteInfo<TakingCareCustomerRouteArgs> {
  TakingCareCustomerRoute({
    required String? assignId,
    required String? workId,
    required String? roomCode,
    required PxTaskListItem? customer,
    required PxRecheckAssignsFetchDetailInfo? workData,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          TakingCareCustomerRoute.name,
          args: TakingCareCustomerRouteArgs(
            assignId: assignId,
            workId: workId,
            roomCode: roomCode,
            customer: customer,
            workData: workData,
            key: key,
          ),
          rawPathParams: {
            'assignId': assignId,
            'workId': workId,
            'roomCode': roomCode,
          },
          initialChildren: children,
        );

  static const String name = 'TakingCareCustomerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TakingCareCustomerRouteArgs>();
      return TakingCareCustomerPage(
        assignId: args.assignId,
        workId: args.workId,
        roomCode: args.roomCode,
        customer: args.customer,
        workData: args.workData,
        key: args.key,
      );
    },
  );
}

class TakingCareCustomerRouteArgs {
  const TakingCareCustomerRouteArgs({
    required this.assignId,
    required this.workId,
    required this.roomCode,
    required this.customer,
    required this.workData,
    this.key,
  });

  final String? assignId;

  final String? workId;

  final String? roomCode;

  final PxTaskListItem? customer;

  final PxRecheckAssignsFetchDetailInfo? workData;

  final Key? key;

  @override
  String toString() {
    return 'TakingCareCustomerRouteArgs{assignId: $assignId, workId: $workId, roomCode: $roomCode, customer: $customer, workData: $workData, key: $key}';
  }
}

/// generated route for
/// [TicketDetailOtherPage]
class TicketDetailOtherRoute extends PageRouteInfo<TicketDetailOtherRouteArgs> {
  TicketDetailOtherRoute({
    Key? key,
    required String id,
    List<PageRouteInfo>? children,
  }) : super(
          TicketDetailOtherRoute.name,
          args: TicketDetailOtherRouteArgs(
            key: key,
            id: id,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'TicketDetailOtherRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<TicketDetailOtherRouteArgs>(
          orElse: () =>
              TicketDetailOtherRouteArgs(id: pathParams.getString('id')));
      return TicketDetailOtherPage(
        key: args.key,
        id: args.id,
      );
    },
  );
}

class TicketDetailOtherRouteArgs {
  const TicketDetailOtherRouteArgs({
    this.key,
    required this.id,
  });

  final Key? key;

  final String id;

  @override
  String toString() {
    return 'TicketDetailOtherRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [TicketDetailPage]
class TicketDetailRoute extends PageRouteInfo<TicketDetailRouteArgs> {
  TicketDetailRoute({
    Key? key,
    required String id,
    List<PageRouteInfo>? children,
  }) : super(
          TicketDetailRoute.name,
          args: TicketDetailRouteArgs(
            key: key,
            id: id,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'TicketDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<TicketDetailRouteArgs>(
          orElse: () => TicketDetailRouteArgs(id: pathParams.getString('id')));
      return TicketDetailPage(
        key: args.key,
        id: args.id,
      );
    },
  );
}

class TicketDetailRouteArgs {
  const TicketDetailRouteArgs({
    this.key,
    required this.id,
  });

  final Key? key;

  final String id;

  @override
  String toString() {
    return 'TicketDetailRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [TicketPage]
class TicketRoute extends PageRouteInfo<void> {
  const TicketRoute({List<PageRouteInfo>? children})
      : super(
          TicketRoute.name,
          initialChildren: children,
        );

  static const String name = 'TicketRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const TicketPage();
    },
  );
}

/// generated route for
/// [UnknownRoutePage]
class UnknownRouteRoute extends PageRouteInfo<UnknownRouteRouteArgs> {
  UnknownRouteRoute({
    Key? key,
    String? route,
    List<PageRouteInfo>? children,
  }) : super(
          UnknownRouteRoute.name,
          args: UnknownRouteRouteArgs(
            key: key,
            route: route,
          ),
          initialChildren: children,
        );

  static const String name = 'UnknownRouteRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<UnknownRouteRouteArgs>(
          orElse: () => const UnknownRouteRouteArgs());
      return UnknownRoutePage(
        key: args.key,
        route: args.route,
      );
    },
  );
}

class UnknownRouteRouteArgs {
  const UnknownRouteRouteArgs({
    this.key,
    this.route,
  });

  final Key? key;

  final String? route;

  @override
  String toString() {
    return 'UnknownRouteRouteArgs{key: $key, route: $route}';
  }
}

/// generated route for
/// [UserListPage]
class UserListRoute extends PageRouteInfo<UserListRouteArgs> {
  UserListRoute({
    Key? key,
    required List<CreateChatGroupUserLoadItems?> currentUserList,
    required ChatListItems? conversation,
    List<PageRouteInfo>? children,
  }) : super(
          UserListRoute.name,
          args: UserListRouteArgs(
            key: key,
            currentUserList: currentUserList,
            conversation: conversation,
          ),
          initialChildren: children,
        );

  static const String name = 'UserListRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<UserListRouteArgs>();
      return UserListPage(
        key: args.key,
        currentUserList: args.currentUserList,
        conversation: args.conversation,
      );
    },
  );
}

class UserListRouteArgs {
  const UserListRouteArgs({
    this.key,
    required this.currentUserList,
    required this.conversation,
  });

  final Key? key;

  final List<CreateChatGroupUserLoadItems?> currentUserList;

  final ChatListItems? conversation;

  @override
  String toString() {
    return 'UserListRouteArgs{key: $key, currentUserList: $currentUserList, conversation: $conversation}';
  }
}

/// generated route for
/// [UserTicketPage]
class UserTicketRoute extends PageRouteInfo<void> {
  const UserTicketRoute({List<PageRouteInfo>? children})
      : super(
          UserTicketRoute.name,
          initialChildren: children,
        );

  static const String name = 'UserTicketRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const UserTicketPage();
    },
  );
}

/// generated route for
/// [VPNConnectedPage]
class VPNConnectedRoute extends PageRouteInfo<void> {
  const VPNConnectedRoute({List<PageRouteInfo>? children})
      : super(
          VPNConnectedRoute.name,
          initialChildren: children,
        );

  static const String name = 'VPNConnectedRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const VPNConnectedPage();
    },
  );
}

/// generated route for
/// [WebViewPage]
class WebViewRoute extends PageRouteInfo<WebViewRouteArgs> {
  WebViewRoute({
    Key? key,
    required String? url,
    String queryParams = '{}',
    bool isHybridComposition = true,
    bool isShowAppBar = true,
    List<PageRouteInfo>? children,
  }) : super(
          WebViewRoute.name,
          args: WebViewRouteArgs(
            key: key,
            url: url,
            queryParams: queryParams,
            isHybridComposition: isHybridComposition,
            isShowAppBar: isShowAppBar,
          ),
          rawQueryParams: {
            'url': url,
            'queryParams': queryParams,
            'isHybridComposition': isHybridComposition,
          },
          initialChildren: children,
        );

  static const String name = 'WebViewRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final queryParams = data.queryParams;
      final args = data.argsAs<WebViewRouteArgs>(
          orElse: () => WebViewRouteArgs(
                url: queryParams.optString('url'),
                queryParams: queryParams.getString(
                  'queryParams',
                  '{}',
                ),
                isHybridComposition: queryParams.getBool(
                  'isHybridComposition',
                  true,
                ),
              ));
      return WebViewPage(
        key: args.key,
        url: args.url,
        queryParams: args.queryParams,
        isHybridComposition: args.isHybridComposition,
        isShowAppBar: args.isShowAppBar,
      );
    },
  );
}

class WebViewRouteArgs {
  const WebViewRouteArgs({
    this.key,
    required this.url,
    this.queryParams = '{}',
    this.isHybridComposition = true,
    this.isShowAppBar = true,
  });

  final Key? key;

  final String? url;

  final String queryParams;

  final bool isHybridComposition;

  final bool isShowAppBar;

  @override
  String toString() {
    return 'WebViewRouteArgs{key: $key, url: $url, queryParams: $queryParams, isHybridComposition: $isHybridComposition, isShowAppBar: $isShowAppBar}';
  }
}
