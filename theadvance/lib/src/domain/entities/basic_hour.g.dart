// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'basic_hour.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BasicHourAdapter extends TypeAdapter<BasicHour> {
  @override
  final int typeId = 154;

  @override
  BasicHour read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BasicHour(
      hour: fields[0] as int,
      minute: fields[1] as int,
    );
  }

  @override
  void write(BinaryWriter writer, BasicHour obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.hour)
      ..writeByte(1)
      ..write(obj.minute);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BasicHourAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
