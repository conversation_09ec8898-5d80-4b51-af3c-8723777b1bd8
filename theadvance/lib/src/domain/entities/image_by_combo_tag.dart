import 'combo_tag.dart';

class ImageByComboTag {
  ImageByComboTag({this.createdDate, required this.images});

  final String? createdDate;

  final List<ImageByComboTagImages?> images;
}

class ImageByComboTagImages {
  ImageByComboTagImages({
    this.id,
    this.customerCode,
    this.imageId,
    this.imageUrl,
    this.imageCreateDate,
    this.isTagged,
    required this.tags,
  });

  final String? id;

  final String? customerCode;

  final String? imageId;

  final String? imageUrl;

  final String? imageCreateDate;

  final bool? isTagged;

  final List<ComboTag>? tags;
}
