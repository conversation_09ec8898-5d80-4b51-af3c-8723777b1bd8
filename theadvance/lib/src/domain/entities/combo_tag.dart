// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:flutter/foundation.dart';

@immutable
class ComboTag {
  const ComboTag({
    this.tagId,
    this.tagName,
    this.tagNameUnicode,
    this.itemGroupId,
    this.itemGroupName,
  });

  final String? tagId;

  final String? tagName;

  final String? tagNameUnicode;

  final String? itemGroupId;

  final String? itemGroupName;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'tagId': tagId,
      'tagName': tagName,
      'tagNameUnicode': tagNameUnicode,
      'itemGroupId': itemGroupId,
      'itemGroupName': itemGroupName,
    };
  }

  String toJson() => json.encode(toMap());

  @override
  bool operator ==(covariant final ComboTag other) {
    if (identical(this, other)) {
      return true;
    }
  
    return 
      other.tagId == tagId &&
      other.tagName == tagName &&
      other.tagNameUnicode == tagNameUnicode &&
      other.itemGroupId == itemGroupId &&
      other.itemGroupName == itemGroupName;
  }

  @override
  int get hashCode {
    return tagId.hashCode ^
      tagName.hashCode ^
      tagNameUnicode.hashCode ^
      itemGroupId.hashCode ^
      itemGroupName.hashCode;
  }
}
