import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

import '../../../core/params/image_by_combo_tag_params.dart';
import '../../entities/image_by_combo_tag.dart';
import '../../repositories/tag_image_repository.dart';

@injectable
class GetImageByComboTagUsecae
    implements
        UseCase<DataState<List<ImageByComboTag>?>, ImageByComboTagQueryParams> {
  GetImageByComboTagUsecae(this._repository);

  final TagImageRepository _repository;

  @override
  Future<DataState<List<ImageByComboTag>?>> call({
    required final ImageByComboTagQueryParams params,
  }) {
    return _repository.getImageByComboTag(params);
  }
}
