import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

import '../../../core/params/combo_tag_request_params.dart';
import '../../entities/combo_tag.dart';
import '../../repositories/tag_image_repository.dart';

@injectable
class GetComboTagUsecase
    implements UseCase<DataState<List<ComboTag>?>, ComboTagRequestParams> {
  GetComboTagUsecase(this._repository);

  final TagImageRepository _repository;

  @override
  Future<DataState<List<ComboTag>?>> call({
    required final ComboTagRequestParams params,
  }) {
    return _repository.getComboTag(params);
  }
}
