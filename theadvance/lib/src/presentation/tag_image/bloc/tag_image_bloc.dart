import 'dart:async';

import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';

import '../../../core/params/combo_tag_request_params.dart';
import '../../../core/params/image_by_combo_tag_params.dart';
import '../../../core/params/request_params.dart';
import '../../../domain/usecases/customer/get_room_list_customer_usecase.dart';
import '../../../domain/usecases/tag_image/get_combo_tag_usecase.dart';
import '../../../domain/usecases/tag_image/get_image_by_combo_tag_usecase.dart';

part 'tag_image_event.dart';
part 'tag_image_state.dart';

@injectable
class TagImageBloc extends Bloc<TagImageEvent, TagImageState> {
  TagImageBloc(this._getRoomListUseCase, this._getComboTagUseCase, this._getImageByComboTagUseCase)
    : super(const TagImageState(TagImageStatus.init)) {
    on<TagImageRoomsGet>(_onTagImageRoomsGetStarted);
    on<GetComboTagEvent>(_onComboTagGetStarted);
    on<ImageByComboTagGet>(_onImageByComboTagGetStarted);
  }
  final GetRoomListCustomerUseCase _getRoomListUseCase;
  final GetComboTagUsecase _getComboTagUseCase;
  final GetImageByComboTagUsecae _getImageByComboTagUseCase;
  FutureOr<void> _onTagImageRoomsGetStarted(
    final TagImageRoomsGet event,
    final Emitter<TagImageState> emit,
  ) async {
    emit(state.copyWith(TagImageStatus.loading));
    final dataState = await _getRoomListUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(state.copyWith(TagImageStatus.success, data: dataState.data));
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(TagImageStatus.failure, data: dataState.error));
    }
  }
  FutureOr<void> _onComboTagGetStarted(
    final GetComboTagEvent event,
    final Emitter<TagImageState> emit,
  ) async {
    emit(state.copyWith(TagImageStatus.loading));
    final dataState = await _getComboTagUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(state.copyWith(TagImageStatus.comboTagSuccess, data: dataState.data));
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(TagImageStatus.failure, data: dataState.error));
    }
  }
    FutureOr<void> _onImageByComboTagGetStarted(
    final ImageByComboTagGet event,
    final Emitter<TagImageState> emit,
  ) async {
    emit(state.copyWith(TagImageStatus.loading));
    final dataState = await _getImageByComboTagUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(state.copyWith(TagImageStatus.imageByComboTagSuccess,
       data: dataState.data));
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(TagImageStatus.failure, data: dataState.error));
    }
  }
}
