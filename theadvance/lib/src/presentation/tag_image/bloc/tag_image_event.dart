part of 'tag_image_bloc.dart';

@immutable
abstract class TagImageEvent {}

class TagImageHistoryGet extends TagImageEvent {
  TagImageHistoryGet();
  // final String setId;
}

class TagImageRoomsGet extends TagImageEvent {
  TagImageRoomsGet(this.params);
  final CustomerGetRoomListRequestParams params;
}

class GetComboTagEvent extends TagImageEvent {
  GetComboTagEvent(this.params);
  final ComboTagRequestParams params;
}

class ImageByComboTagGet extends TagImageEvent {
  ImageByComboTagGet(this.params);
  final ImageByComboTagQueryParams params;
}
