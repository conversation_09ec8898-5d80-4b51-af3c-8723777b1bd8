part of 'tag_image_bloc.dart';

enum TagImageStatus {
  init,
  loading,
  success,
  comboTagSuccess,
  imageByComboTagSuccess,
  uploadSuccess,
  updateSuccess,
  onlySetSuccess,
  removeSetSuccess,
  updateSetSuccess,
  failure,
}

@immutable
class TagImageState extends Equatable {
  const TagImageState(this.status, {this.data});
  final dynamic data;
  final TagImageStatus status;

  TagImageState copyWith<T>(final TagImageStatus status, {final dynamic data}) {
    return TagImageState(status, data: data);
  }

  @override
  List<Object?> get props => [status, data];
}
