import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../../core/routes/app_router.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/ez_datasources.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../collaborator/home/<USER>';
import '../../widgets/widgets.dart';
import '../bloc/tag_image_bloc.dart';

class TagImageBody extends StatefulWidget {
  const TagImageBody({super.key, required this.rooms});
   final ValueNotifierList<CustomerGetRoomListItem?> rooms;
  @override
  State<TagImageBody> createState() => _TagImageBodyState();
}

class _TagImageBodyState extends State<TagImageBody> {
  final ValueNotifierList<CustomerGetRoomListItem?> rooms = ValueNotifierList(
    [],
  );
  CustomerGetRoomList? data;
  List<String> histories = [];
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((final t) async {
      final history = await EZCache.shared.getHistoryRecent();
      histories = history;
    });
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<TagImageBloc, TagImageState>(
      listener: (final context, final state) {
        if (state.status == TagImageStatus.success) {
          data = Utils.getData(state.data);
          rooms.setValue(data?.items);
          // unawaited(
          //   context.router.push(
          //     ChatRoute(conversationId: data?.conversation?.id),
          //   ),
          // );
        }
      },
      builder: (final context, final state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Column(
            spacing: 12,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildComponent(
                'data',
                body: ValueListenableBuilder(
                  valueListenable: rooms,
                  builder: (final context, final vServices, final child) {
                    final buildServiceWidgets = vServices
                        .map(
                          (final e) => HomeMenuItem(
                            url: e?.iconUrl,
                            name: e?.roomName,
                            ontap: () {
                               context.router.push(
            TagByResultImageListRoute(
              tags: [],
              rooms: vServices
                  .map(
                    (final e) => e ?? CustomerGetRoomListItem(),
                  )
                  .toList(),
            ),
          );
                              // context.router.push(
                              //   TagByResultImageListRoute(
                              //     tags: [e?.roomName ?? ''],
                              //     rooms: rooms.value
                              //         .map(
                              //           (final e) =>
                              //               e ?? CustomerGetRoomListItem(),
                              //         )
                              //         .toList(),
                              //   ),
                              // );
                            },
                          ),
                        )
                        .toList();
                    return GridView.count(
                      crossAxisCount: 3,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      childAspectRatio: 0.9,
                      children: buildServiceWidgets,
                    );
                  },
                ),
              ),
              _buildComponent(
                'data 2',
                body: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: SizedBox(
                      child: Wrap(
                        children: List.generate(histories.length, (final i) {
                          return GestureDetector(
                            onTap: () async {
                              context.router.push(
                                TagByResultImageListRoute(
                                  tags: [histories[i]],
                                  rooms: rooms.value
                                      .map(
                                        (final e) =>
                                            e ?? CustomerGetRoomListItem(),
                                      )
                                      .toList(),
                                ),
                              );
                            },
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    width: .35,
                                    color: Theme.of(context).hintColor,
                                  ),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(histories[i]),
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildComponent(final String title, {required final Widget body}) {
    return Column(children: [Text(title), body]);
  }
}
