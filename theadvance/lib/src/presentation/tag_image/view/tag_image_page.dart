import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

import '../../../core/params/request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../../injector/injector.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../../widgets/widgets.dart';
import '../bloc/tag_image_bloc.dart';
import '../widget/tag_image_body.dart';

@RoutePage()
class TagImagePage extends StatefulWidget {
  const TagImagePage({super.key});

  @override
  State<TagImagePage> createState() => _TagImagePageState();
}

class _TagImagePageState extends State<TagImagePage> {
  final ValueNotifierList<CustomerGetRoomListItem?> rooms = ValueNotifierList(
    [],
  );
  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) =>
          getIt<TagImageBloc>()
            ..add(TagImageRoomsGet(const CustomerGetRoomListRequestParams())),
      child: BaseLayout(
        body: TagImageBody(rooms: rooms),
        title: Text(
          context.l10n.approvalList,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        bottomAppBarChild: BlocBuilder<TagImageBloc, TagImageState>(
          builder: (final context, final state) {
            if (state.status == TagImageStatus.success) {
              return Padding(
                padding: const EdgeInsets.all(12.0),
                child: Theme(
                  data: ThemeData(
                    inputDecorationTheme: InputDecorationTheme(
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                  child: TextField(
                    readOnly: true,
                    onTap: () {
                      context.router
                          .push(
                            TagImageSearchRoute(
                              rooms: rooms.value
                                  .map(
                                    (final e) => e ?? CustomerGetRoomListItem(),
                                  )
                                  .toList(),
                              isFromTagImagePage: true,
                            ),
                          )
                          .whenComplete(() async {
                            print('rebuilddddd');
                            final history = await EZCache.shared
                                .getHistoryRecent();
                            // histories = history;
                            //  tagsRecent =
                            //   getIt<Mapper>().convertList(
                            //     histories
                            //         .map((final e) => ComboTagModel.fromJson(json.decode(e)))
                            //         .toList(),
                            //   );
                            //   setState(() {});
                          });
                    },
                    decoration: InputDecoration(
                      suffixIconConstraints: const BoxConstraints(
                        minWidth: 24,
                        minHeight: 24,
                      ),
                      prefixIconConstraints: const BoxConstraints(
                        minWidth: 24,
                        minHeight: 24,
                      ),
                      prefixIcon: Padding(
                        padding: const EdgeInsets.only(left: 12),
                        child: EZResources.image(
                          ImageParams(
                            name: AppIcons.icSearch,
                            color: Theme.of(context).hintColor,
                          ),
                        ),
                      ),
                      contentPadding: const EdgeInsets.only(
                        left: 12,
                        top: 22,
                        right: 22,
                      ),
                      hint: Text(
                        context.l10n.search,
                        style: TextStyle(color: Theme.of(context).hintColor),
                      ),
                      isDense: true,
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}
