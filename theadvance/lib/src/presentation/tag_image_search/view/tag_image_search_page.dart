import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

import '../../../core/params/combo_tag_request_params.dart';
import '../../../core/routes/routes.dart';
import '../../../core/utils/mappers.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../data/models/combo_tag_model.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../../injector/injector.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../../tag_image/bloc/tag_image_bloc.dart';
import '../../widgets/value_notifier_list.dart';
import '../widget/tag_image_search_body.dart';

@RoutePage()
class TagImageSearchPage extends StatefulWidget {
  const TagImageSearchPage({super.key,
  required this.rooms, required this.isFromTagImagePage});
  final List<CustomerGetRoomListItem> rooms;
  final bool isFromTagImagePage;
  @override
  State<TagImageSearchPage> createState() => _TagImageSearchPageState();
}

class _TagImageSearchPageState extends State<TagImageSearchPage> {
  late TextEditingController controller;
  final ValueNotifierList<ComboTag> tags = ValueNotifierList([]);
  List<String> histories = [];
  List<ComboTag> tagsRecent = [];
  @override
  void initState() {
    controller = TextEditingController(text: '');
    WidgetsBinding.instance.addPostFrameCallback((final t) async {
      final history = await EZCache.shared.getHistoryRecent();
      histories = history;
      tagsRecent =
        getIt<Mapper>().convertList(
          histories
              .map((final e) => ComboTagModel.fromJson(json.decode(e)))
              .toList(),
        );
    });
    super.initState();
  }
  List<ComboTag>? data;
  List<ComboTag>? dataFinal;
  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) => getIt<TagImageBloc>()
      ..add(GetComboTagEvent(const ComboTagRequestParams())),
      child: BaseLayout(
        title: Text(
          context.l10n.picture,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        bottomAppBarChild: BlocConsumer<TagImageBloc, TagImageState>(
          listener: (final context, final state) {
            if (state.status == TagImageStatus.comboTagSuccess) {
               data = Utils.getData(state.data);
               dataFinal = data?.where((final e)
              => !tagsRecent.contains(e)).toList();
              tags.setValue(dataFinal);
            }
          },
          builder: (final context, final state) {
            return Padding(
              padding: const EdgeInsets.all(12.0),
              child: Theme(
                data: ThemeData(
                  inputDecorationTheme: InputDecorationTheme(
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
                child: TextField(
                  controller: controller,
                  autofocus: true,
                  onChanged: (final value) {
                    if (value.isEmpty) {
                      tags.setValue(dataFinal);
                      return;
                    }else{
                      tags.setValue(dataFinal?.where((final e)
                      => e.tagName?.toLowerCase().contains(value.toLowerCase())
                      ?? false).toList());
                    }
                   
                   
                  },
                  decoration: InputDecoration(
                    suffixIconConstraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                    prefixIconConstraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                    prefixIcon: Padding(
                      padding: const EdgeInsets.only(left: 12),
                      child: EZResources.image(
                        ImageParams(
                          name: AppIcons.icSearch,
                          color: Theme.of(context).hintColor,
                        ),
                      ),
                    ),
                    contentPadding: const EdgeInsets.only(
                      left: 12,
                      top: 22,
                      right: 22,
                    ),
                    hint: Text(
                      context.l10n.search,
                      style: TextStyle(color: Theme.of(context).hintColor),
                    ),
                    isDense: true,
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),
              ),
            );
          },
        ),
        body: TagImageSearchBody(tags: tags,
         rooms: widget.rooms,
         isFromTagImagePage: widget.isFromTagImagePage),
      ),
    );
  }
}
