import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../../core/routes/app_router.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../widgets/value_notifier_list.dart';

class TagImageSearchBody extends StatelessWidget {
   const TagImageSearchBody({super.key, required this.tags,
    required this.rooms, required this.isFromTagImagePage});
  final ValueNotifierList<ComboTag> tags;
  final List<CustomerGetRoomListItem> rooms;
  final bool isFromTagImagePage;
  @override
  Widget build(final BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: DecoratedBox(
        decoration: BoxDecoration(
       
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: SizedBox.expand(
          child: ValueListenableBuilder(
            valueListenable: tags,
            builder: (final context, final vTags, final child) {
              return Wrap(
                children: [
                  for (final ComboTag tag in vTags) 
                  _buildChip(context, tag),
                 
                ],
              );
            },
          ),
        ),
      ),
    );
  }
  Widget _buildChip(final BuildContext context, final ComboTag tag){
    return GestureDetector(
      onTap: ()async{
        if(isFromTagImagePage){
          context.router.replace(
            TagByResultImageListRoute(
              tags: [tag.toJson()],
              rooms: rooms
                  .map(
                    (final e) => e,
                  )
                  .toList(),
            ),
          );
        }else{
          context.router.popForced();
        }
         
        final histories = await EZCache.shared.getHistoryRecent();
        histories.add(tag.toJson());
        EZCache.shared.saveHistoryRecent(
          histories
        );
       
      },
      child: DecoratedBox(
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      width: .35,
                                      color: Theme.of(context).hintColor,
                                    ),
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(tag.tagName ?? ''),
                                ),
                              ),
    );
  }
}
