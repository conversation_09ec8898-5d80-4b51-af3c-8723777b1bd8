// Dart imports:
import 'dart:async';
import 'dart:convert';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:app_config/app_config.dart';
import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_qr_code/ez_qr_code.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent_bottom_nav_bar_v2.dart';

// Project imports:
import '../../../../core/enums/room_code.dart';
import '../../../../core/nd_constants/strings.dart';
import '../../../../core/params/request_params.dart';
import '../../../../core/routes/app_router.dart';
import '../../../../core/routes/routes.dart';
import '../../../../core/utils/app_permission_helper.dart';
import '../../../../core/utils/nd_utils.dart';
import '../../../../data/datasources/ez_datasources.dart';
import '../../../../data/models/nd_models.dart';
import '../../../_blocs/general_bloc/general_bloc.dart';
import '../../../branch_selection/view/branch_selection_page.dart';
import '../../../customer_record/widgets/customer_record_list_sheet.dart';
import '../../../hr_organization/view/hr_organization_page.dart';
import '../../../widgets/widgets.dart';
import '../bloc/bloc.dart';
import '../models/home_menu.dart';

class HomeMenuItem extends StatelessWidget {
  const HomeMenuItem({
    final Key? key,
    this.service,
    this.url,
    this.name,
    this.ontap,
  }) : super(key: key);
  final ServiceItemsModel? service;
  final String? url;
  final String? name;
  final Function()? ontap;

  @override
  Widget build(final BuildContext context) {
    return InkWell(
      onTap: () async => ontap?.call() ?? onTapItem(context),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),

        child: SizedBox(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                if (url != null)
                  EzCachedNetworkImage(
                    imageUrl: url,
                    fit: BoxFit.contain,
                    width: _itemWidth(context),
                    height: _itemWidth(context),
                  )
                else
                  ShimmerWrapper(
                    baseColor: Colors.grey.shade300,
                    highlightColor: Colors.grey.shade100,
                    child: Container(
                      decoration: const ShapeDecoration(
                        shape: CircleBorder(),
                        color: Colors.white,
                      ),

                      height: _itemWidth(context),
                    ),
                  ),

                if (name != null)
                  Text(
                    name ?? '',
                    textScaler: TextScaler.noScaling,
                    maxLines: 2,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.labelLarge,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  //ignore: long-method
  Future<void> onTapItem(final BuildContext context) async {
    final value = HomeMenu.values.firstWhereOrNull(
      (final element) => element.name.toUpperCase() == service?.code,
    );
    if (value != null) {
      switch (value) {
        case HomeMenu.search_customer:
          {
            await scanCustomerQRCode(context);
            break;
          }
        case HomeMenu.scan_qr_code:
          {
            await scanQRCode(context);
            break;
          }
        case HomeMenu.zema_record:
          {
            await CustomerRecordListSheet.show(context);
            break;
          }
        case HomeMenu.create_customer:
          {
            await context.router.pushNamed(Routes.createCustomer);
            break;
          }
        case HomeMenu.advisory:
          {
            context.read<HomeBloc>().add(
              HomeConsultationLeaderChecked(
                HomeWorkLeaderCheckRequestParams(roomCode: RoomCode.tuvan.name),
              ),
            );
            break;
          }
        case HomeMenu.working:
          {
            context.read<HomeBloc>().add(
              HomeGetRoomList(const CustomerGetRoomListRequestParams()),
            );

            break;
          }
        case HomeMenu.create_request:
          {
            if (context.mounted) {
              await AutoRouter.of(context).pushNamed(Routes.eform);
            }
            break;
          }
        case HomeMenu.collaborator_news:
          {
            if (context.mounted) {
              await AutoRouter.of(context).pushNamed(Routes.news);
            }
            break;
          }
        case HomeMenu.inventory:
          {
            if (context.mounted) {
              await AutoRouter.of(context).pushNamed(Routes.accLanding);
            }
            break;
          }
        case HomeMenu.event:
          {
            if (context.mounted) {
              await AutoRouter.of(context).pushNamed(Routes.eventSplash);
            }
            break;
          }
        case HomeMenu.HR360:
          {
            if (context.mounted) {
              await AutoRouter.of(context).pushNamed(
                '${Routes.hrOrganization}/'
                '${ToNextPage.HR.name}',
              );
            }
            break;
          }
        case HomeMenu.history:
          break;
        case HomeMenu.business:
          {
            if (context.mounted) {
              await handleBusinessFlow(context);
            }
            break;
          }
        case HomeMenu.customer_booking:
          {
            checkLeader(context);
            break;
          }
        case HomeMenu.customer_care:
          {
            if (context.mounted) {
              final user = EZCache.shared.getUserProfile();
              if (user?.chat?.isTakeCareAll ?? false) {
                context.router.pushNamed(Routes.chatSelectBranch);
              } else {
                context.router.push(
                  BranchChatListRoute(
                    branchId: user?.branchId,
                    branchName: user?.branchName,
                  ),
                );
              }
            }
            break;
          }
        case HomeMenu.chat_internal:
          {
            await _onTapChatInternal(context);
            break;
          }
        case HomeMenu.crm:
          {
            await context.router.pushNamed(Routes.userTicket);
            break;
          }
        case HomeMenu.media:
          {
            await context.router.pushNamed(Routes.media);
            break;
          }
        case HomeMenu.booking_meal:
          {
            await context.router.pushNamed(Routes.orderFood);
            break;
          }
        case HomeMenu.ticket:
          {
            if (context.mounted) {
              await AutoRouter.of(context).pushNamed(Routes.tagImage);
            }
            break;
            // TODO(lthung) router
            // await context.router.pushNamed(Routes.ticket);
            // break;
          }
        case HomeMenu.report_kpi:
          {
            await context.router.pushNamed(Routes.kpiEmployee);
            break;
          }
        case HomeMenu.approved_purchase_contract:
          {
            {
              if (context.mounted) {
                await AutoRouter.of(context).pushNamed(
                  '${Routes.hrOrganization}/'
                  '${ToNextPage.PRODUCT_CONFIRM.name}',
                );
              }
              break;
            }
          }
        case HomeMenu.check_in:
          {
            await context.router.pushNamed(Routes.checkin);
            break;
          }
        case HomeMenu.mini_app:
          {
            context.router.push(
              WebViewRoute(
                url: service?.webAppUrl,
                queryParams: '{"accessToken":"${EZCache.shared.accessToken}"}',
                isShowAppBar: false,
              ),
            );
            break;
          }
        case HomeMenu.customer_image:
          {
            await context.router.pushNamed(Routes.customerImage);
            break;
          }
        case HomeMenu.none:
          break;
      }
    }
  }

  Future<void> _onTapChatInternal(final BuildContext context) async {
    final employeeId = EZCache.shared.getUserProfile()?.employeeId;
    final url = AppFlavor.appFlavor == Flavor.production
        ? 'https://chat.ngocdunggroup.com.vn/user?product=mobile-theadvance-app&token=${Strings.chatToken}&username=$employeeId'
        : 'https://chat-dev.ngocdunggroup.com.vn/user?product=mobile-theadvance-app&token=${Strings.chatToken}&username=$employeeId';
    AutoRouter.of(context).push(WebViewRoute(url: url));
  }

  Future<void> handleBusinessFlow(final BuildContext context) async {
    pushScreen<void>(
      context,
      screen: const PushScreenWithNavBarWrapper(child: BranchSelectionPage()),
      withNavBar: true,
    );
  }

  void checkLeader(final BuildContext context) {
    final empCode = Utils.defaultOnEmpty(
      EZCache.shared.getUserProfile()?.employeeId,
    );
    context.read<HomeBloc>().add(
      HomeCheckedLeader(HomeCheckLeaderRequestParams(empCode: empCode)),
    );
  }

  Future<void> scanCustomerQRCode(final BuildContext context) async {
    return EzQRCode.scanQRCode(
      ScanQRCodeParams(
        context,
        isPop: false,
        cameraPermissionDeniedHandler: () async {
          EzToast.showToast(message: context.l10n.requestPermissionCamera);
        },
        titleColor: Theme.of(context).colorScheme.surface,
        titleAppBar: context.l10n.scan,
        qrCornerColor: Theme.of(context).colorScheme.surface,
        callBack: (final qrCode) {
          _callBackScanQrCode(qrCode, context);
        },
        onRequestCameraPermission: () =>
            AppPermissionHelper.requestCameraPermission(context),
        onRequestGalleryPermission: () =>
            AppPermissionHelper.requestPhotosPermission(context),
        galleryPermissionDeniedHandler: () async {
          EzToast.showToast(message: context.l10n.requestPermissionLibrary);
        },
        secondaryButton: _buildManualCheckinButton(context),
      ),
    );
  }

  Future<void> scanQRCode(final BuildContext context) async {
    return EzQRCode.scanQRCode(
      ScanQRCodeParams(
        context,
        cameraPermissionDeniedHandler: () async {
          EzToast.showToast(message: context.l10n.requestPermissionCamera);
        },
        titleColor: Theme.of(context).colorScheme.surface,
        titleAppBar: context.l10n.scanQRCode,
        qrCornerColor: Theme.of(context).colorScheme.surface,
        callBack: (final qrCode) {
          context.read<GeneralBloc>().add(
            GeneralUniversalQrScanned(code: qrCode),
          );
        },
        onRequestCameraPermission: () =>
            AppPermissionHelper.requestCameraPermission(context),
        onRequestGalleryPermission: () =>
            AppPermissionHelper.requestPhotosPermission(context),
        galleryPermissionDeniedHandler: () async {
          EzToast.showToast(message: context.l10n.requestPermissionLibrary);
        },
      ),
    );
  }

  ElevatedButton _buildManualCheckinButton(final BuildContext context) {
    return ElevatedButton(
      onPressed: () async => context.router.pushNamed(Routes.customerList),
      style: ElevatedButton.styleFrom(
        shape: StadiumBorder(
          side: BorderSide(color: Theme.of(context).primaryColor),
        ),
      ),
      child: Text(
        context.l10n.manualCheckin,
        textAlign: TextAlign.center,
        style: Theme.of(
          context,
        ).textTheme.labelLarge?.copyWith(color: Theme.of(context).primaryColor),
      ),
    );
  }

  void _callBackScanQrCode(final String? qrCode, final BuildContext context) {
    if (qrCode?.isNotEmpty ?? false) {
      try {
        final customer = CustomerInfoDetailsModel.fromJson(
          json.decode(qrCode!.replaceAll(r'\', '')) as Map<String, dynamic>,
        );
        Navigator.of(context).pop();
        unawaited(
          AutoRouter.of(context).push(
            CustomerRoute(
              id: Utils.defaultOnEmpty(customer.customerId),
              isScanQrCode: true,
            ),
          ),
        );
      } catch (_) {
        unawaited(
          Alert.showAlert(
            AlertParams(context, context.l10n.invalidScannedCode),
          ),
        );
      }
    } else {
      unawaited(
        Alert.showAlert(AlertParams(context, context.l10n.invalidScannedCode)),
      );
    }
  }

  double _itemWidth(final BuildContext context) {
    return MediaQuery.sizeOf(context).width / 8.5;
  }
}
