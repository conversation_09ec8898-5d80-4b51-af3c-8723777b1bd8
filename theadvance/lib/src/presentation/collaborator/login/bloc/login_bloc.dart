// Dart imports:
import 'dart:async';

// Package imports:
import 'package:app_config/app_config.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_login_authentication/ez_login_authentication.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../../../core/params/request_params.dart';
import '../../../../core/utils/nd_utils.dart';
import '../../../../data/datasources/ez_datasources.dart';
import '../../../../data/models/nd_models.dart';
import '../../../../domain/entities/entities.dart';
import '../../../../domain/entities/login_social.dart';
import '../../../../domain/usecases/login/socket_access_token_get_login_usecase.dart';
import '../../../../domain/usecases/user/cache_user_usecase.dart';
import '../../../../domain/usecases/user/check_permission_user_usecase.dart';
import '../../../../domain/usecases/user/check_phone_usecase.dart';
import '../../../../domain/usecases/user/get_otp_usecase.dart';
import '../../../../domain/usecases/user/has_user_data_usecase.dart';
import '../../../../domain/usecases/user/login_social_usecase.dart';
import '../../../../domain/usecases/user/login_usecase.dart';
import '../../../../services/nd_socket/socket.dart';
import '../../../chat_list/chat_list.dart';
import '../../../consultation_customer/widgets/consultation_customer_body.dart';

// Project imports:

part 'login_event.dart';
part 'login_state.dart';

@injectable
class LoginBloc extends Bloc<LoginEvent, LoginState> {
  LoginBloc(
    this._collaboratorGetOtpUseCase,
    this._collaboratorHasUserDataUseCase,
    this._collaboratorCheckPhoneUseCase,
    this._collaboratorLoginUseCase,
    this._collaboratorCacheUserUseCase,
    this._loginSocialUseCase,
    this._socketAccessTokenGetLoginUseCase,
    this._checkPermissionUserUseCase,
  ) : super(LoginInitial()) {
    on<LoginDataChecked>(
      (final event, final emit) async => handleLoginDataCheck(emit),
    );
    on<LoginSignInInvoked>((final event, final emit) async {
      if (isValid(event)) {
        await handleLoginButtonPress(event, emit);
      } else {
        emit(LoginValidateFailure());
      }
    });
    on<LoginPhoneChecked>(
      (final event, final emit) async => handleCheckPhone(event.phone, emit),
    );
    on<LoginExitAccountPressed>(
      (final event, final emit) async => handleLoginExitAccountPress(emit),
    );
    on<LoginLocalAuthenticationInvoked>(
      (final event, final emit) async =>
          handleLoginLocalAuthenticateInvoke(event, emit),
    );
    on<LoginLocalAuthenticationActivated>(
      (final event, final emit) async => handleLoginLocalAuthenticationActive(),
    );
    on<LoginLocalAuthenticationInactivated>(
      (final event, final emit) async =>
          handleLoginLocalAuthenicationInactive(),
    );
    on<LoginPasswordReset>(
      (final event, final emit) async => handleResetPassword(event.phone, emit),
    );
  }

  final GetOtpUseCase _collaboratorGetOtpUseCase;
  final HasUserDataUseCase _collaboratorHasUserDataUseCase;
  final CheckPhoneUseCase _collaboratorCheckPhoneUseCase;
  final LoginUseCase _collaboratorLoginUseCase;
  final CacheUserUseCase _collaboratorCacheUserUseCase;
  final LoginSocialUseCase _loginSocialUseCase;
  final SocketAccessTokenGetLoginUseCase _socketAccessTokenGetLoginUseCase;
  final CheckPermissionUserUseCase _checkPermissionUserUseCase;

  Future<void> handleResetPassword(
    final String? phone,
    final Emitter<LoginState> emit,
  ) async {
    try {
      emit(LoginGetInfoUserInProgress());
      // await deleteUserData();
      final dataState = await _collaboratorGetOtpUseCase(
        params: GetOtpParams(phone: phone),
      );
      if (dataState is DataSuccess) {
        final response = dataState.data;
        if (response!.errorCode == ErrorCodes.success) {
          emit(LoginGetNewPasswordSuccess(phoneNumber: phone));
        } else {
          emit(
            LoginFailure(
              apiError: ApiError(
                code: response.errorCode,
                message: response.errorMessage,
              ),
            ),
          );
        }
      }
      if (dataState is DataFailure) {
        emit(
          LoginFailure(apiError: ApiError(message: dataState.error?.message)),
        );
      }
    } catch (error) {
      emit(const LoginFailure());
    }
  }

  //  FutureOr<void> _onloginSocial(

  //   final Emitter<LoginState> emit,
  // ) async {

  //     emit(LoginGetInfoUserInProgress());
  //     final dataState =
  //         await _loginSocialUseCase(params: ());
  //        if (dataState is DataSuccess) {
  //     final loginSocial = Utils.getData(dataState.data);
  //     emit(
  //       LoginSocialSuccess(loginSocial),
  //     );
  //   }
  //   if (dataState is DataFailure) {
  //     emit( LoginFailure(
  //               apiError: Utils.getData(dataState.data),
  //             ),
  //     );
  //   }
  // }

  Future<void> handleLoginDataCheck(final Emitter<LoginState> emit) async {
    try {
      emit(LoginGetInfoUserInProgress());
      final user = EZCache.shared.getUserProfile();
      //Delete old data
      if (user == null) {
        await deleteUserData();
      }
      if (await _collaboratorHasUserDataUseCase()) {
        if (await EZSecureStorage.storage.read(
              key: KeyStorage.biometricsAuthen,
            ) !=
            null) {
          emit(LoginLocalAuthenticateEnableSuccess());
        }

        final listBiometrics = await EZAuthentication.getAvailableBiometrics();
        emit(
          LoginGetInfoUserSuccess(
            name: (await _collaboratorCacheUserUseCase()).name,
            phone: await EZSecureStorage.storage.read(key: KeyStorage.username),
            listBiometrics: listBiometrics,
            isActiveBiometrics: true,
          ),
        );
      } else {
        if (user != null) {
          add(LoginDataChecked());
        } else {
          emit(LoginGetInfoUserEmpty());
        }
      }
    } catch (error) {
      emit(LoginGetInfoUserEmpty());
    }
  }

  Future<void> handleCheckPhone(
    final String phone,
    final Emitter<LoginState> emit,
  ) async {
    emit(LoginInProgress());
    try {
      if (phone.phoneNumberValidator()) {
        final dataState = await _collaboratorCheckPhoneUseCase(
          params: CheckPhoneRequestParams(phone: phone),
        );
        if (dataState is DataSuccess) {
          final response = dataState.data;
          if (response!.errorCode == ErrorCodes.success) {
            if (response.data?.isExist == true) {
              var listBiometrics = <BiometricType>[];
              listBiometrics = await EZAuthentication.getAvailableBiometrics();

              emit(
                LoginGetInfoUserSuccess(
                  phone: phone,
                  name:
                      response.data?.name ??
                      (await _collaboratorCacheUserUseCase()).name,
                  listBiometrics: listBiometrics,
                  isActiveBiometrics: false,
                ),
              );
            } else {
              await _loginNew(phone, emit);
            }
          } else {
            emit(
              LoginFailure(apiError: ApiError(message: response.errorMessage)),
            );
          }
        }
        if (dataState is DataFailure) {
          emit(
            LoginFailure(apiError: ApiError(message: dataState.error?.message)),
          );
        }
      } else {
        emit(LoginValidateFailure());
      }
    } catch (_) {
      emit(const LoginFailure());
    }
  }

  bool isValid(final LoginSignInInvoked event) {
    return event.username.isNotEmpty && event.password.isNotEmpty;
  }

  Future<void> handleLoginButtonPress(
    final LoginSignInInvoked event,
    final Emitter<LoginState> emit,
  ) async {
    emit(LoginInProgress());
    try {
      final dataState = await _collaboratorLoginUseCase(
        params: LoginRequestParams(
          phone: event.username,
          password: Utils.encryptPassword(event.password),
        ),
      );
      await ifDataSuccess(dataState, emit, event);
      if (dataState is DataFailure) {
        emit(
          LoginFailure(apiError: ApiError(message: dataState.error?.message)),
        );
      }
    } catch (_) {
      emit(const LoginFailure());
    }
  }

  Future<void> ifDataSuccess(
    final DataState<LoginResponseModel?> dataState,
    final Emitter<LoginState> emit,
    final LoginSignInInvoked event,
  ) async {
    if (dataState is DataSuccess) {
      final response = dataState.data;
      if (response!.errorCode == ErrorCodes.success) {
        await EZCache.shared.saveAccessToken(response.data?.accessToken);
        await EZCache.shared.saveUserProfile(response.data?.profile);
        await EZCache.shared.saveUserPermission(response.data?.permission);

        // Check Permission
        final dataStatePermission = await _checkPermissionUserUseCase(
          params: const UserCheckPermissionRequestParams(),
        );
        if (dataStatePermission is DataSuccess) {
          final UserCheckPermission? dataPermission = Utils.getData(
            dataStatePermission.data,
          );

          ConsultationCustomerBody.accessConsultationContentTab =
              dataPermission
                  ?.permissionBySegment
                  ?.accessConsultationContentTab ??
              true;
          ConsultationCustomerBody.accessConsultationTab =
              dataPermission?.permissionBySegment?.accessConsultationTab ??
              true;
          await EZCache.shared.saveAccessGlobalDomain(
            accessGlobalDomain: dataPermission?.permission?.accessGlobalDomain,
          );
        }
        // Token Social
        final dataloginSocial = await _loginSocialUseCase(params: ());
        if (dataloginSocial is DataSuccess) {
          final LoginSocial dataSocial = Utils.getData(dataloginSocial.data);
          await EZCache.shared.saveAccessTokenSocial(dataSocial.accessToken);
        }
        if (dataloginSocial is DataFailure) {
          final ApiError apiError = Utils.getData(dataloginSocial.error);
          emit(LoginSocialFailure(apiError: apiError));
        }
        //TODO block login chat
        //   await _getSocketAccessToken(emit);
        if (await _collaboratorHasUserDataUseCase()) {
          emit(
            LoginWithoutDialogSuccess(
              user: response.data?.profile,
              username: event.username,
              password: event.password,
            ),
          );
        } else {
          emit(
            LoginShowDialogSuccess(
              user: response.data?.profile,
              username: event.username,
              password: event.password,
            ),
          );
        }
        await saveUserData(username: event.username, password: event.password);
      } else {
        emit(
          LoginFailure(
            apiError: ApiError(
              code: response.errorCode,
              message: response.errorMessage,
            ),
          ),
        );
      }
    }
  }

  Future<void> handleLoginExitAccountPress(
    final Emitter<LoginState> emit,
  ) async {
    try {
      emit(LoginInProgress());
      emit(LoginSignOutSuccess());
      emit(LoginGetInfoUserEmpty());
    } catch (_) {}
  }

  Future<void> handleLoginLocalAuthenticateInvoke(
    final LoginLocalAuthenticationInvoked event,
    final Emitter<LoginState> emit,
  ) async {
    try {
      emit(LoginInProgress());
      if (event.authenticated) {
        emit(LoginLocalAuthenticateSuccess());
        final userName = await EZSecureStorage.storage.read(
          key: KeyStorage.username,
        );
        final password = await EZSecureStorage.storage.read(
          key: KeyStorage.password,
        );
        add(
          LoginSignInInvoked(
            username: Utils.defaultOnEmpty(userName),
            password: Utils.defaultOnEmpty(password),
          ),
        );
      } else {
        emit(LoginLocalAuthenticateFailure());
      }
    } catch (error) {
      emit(LoginLocalAuthenticateFailure());
    }
  }

  Future<void> _loginNew(
    final String phone,
    final Emitter<LoginState> emit,
  ) async {
    final otpDataState = await _collaboratorGetOtpUseCase(
      params: GetOtpParams(phone: phone),
    );
    if (otpDataState is DataSuccess) {
      final otpRes = otpDataState.data;
      if (otpRes!.errorCode == ErrorCodes.success) {
        emit(LoginNewSuccess(phoneNumber: phone));
      } else {
        emit(
          LoginFailure(
            apiError: ApiError(
              code: otpRes.errorCode,
              message: otpRes.errorMessage,
            ),
          ),
        );
      }
    }
    if (otpDataState is DataFailure) {
      emit(
        LoginFailure(apiError: ApiError(message: otpDataState.error?.message)),
      );
    }
  }

  Future<void> _getSocketAccessToken(final Emitter<LoginState> emit) async {
    final dataState = await _socketAccessTokenGetLoginUseCase(
      params: const LoginSocketAccessTokenGetRequestParams(),
    ).timeout(const Duration(seconds: 3));
    if (dataState is DataSuccess) {
      final socketAccessToken = dataState.data?.accessToken;
      if (socketAccessToken != null) {
        await EZCache.shared.saveChatLoginData(dataState.data);
        await EZCache.shared.saveSocketAccessToken(dataState.data?.accessToken);
        SocketService.connect(
          host: AppConfig.chatUrl,
          accessToken: socketAccessToken,
        );

        hasSocketAccessToken.value = true;
      }
    }
  }

  Future<void> handleLoginLocalAuthenticationActive() async {
    try {
      await EZSecureStorage.storage.write(
        key: KeyStorage.biometricsAuthen,
        value: 'enable',
      );
    } catch (_) {}
  }

  Future<void> handleLoginLocalAuthenicationInactive() async {
    try {
      await EZSecureStorage.storage.delete(key: KeyStorage.biometricsAuthen);
    } catch (_) {}
  }

  Future<void> saveUserData({
    final String? username,
    final String? password,
  }) async {
    await EZSecureStorage.storage.write(
      key: KeyStorage.username,
      value: username,
    );
    await EZSecureStorage.storage.write(
      key: KeyStorage.password,
      value: password,
    );
  }

  Future<void> deleteUserData() async {
    await EZSecureStorage.storage.delete(key: KeyStorage.username);
    await EZSecureStorage.storage.delete(key: KeyStorage.password);
    await EZSecureStorage.storage.delete(key: KeyStorage.biometricsAuthen);
  }
}
