import 'package:flutter/material.dart';

import '../../../core/utils/utils.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../../domain/entities/image_by_combo_tag.dart';
import '../../../domain/entities/tag_result_image.dart';
import '../../tag_image/bloc/tag_image_bloc.dart';
import '../../widgets/widgets.dart';

class TagByResultImageListBody extends StatefulWidget {
  const TagByResultImageListBody({
    super.key,
    required this.tags,
    required this.rooms,
  });
  final ValueNotifierList<ComboTag> tags;
  final List<CustomerGetRoomListItem> rooms;
  @override
  State<TagByResultImageListBody> createState() =>
      _TagByResultImageListBodyState();
}

class _TagByResultImageListBodyState extends State<TagByResultImageListBody>
    with TickerProviderStateMixin {
  final ValueNotifierList<ImageByComboTag?> images = ValueNotifierList(
    [],
  );
 
  late TabController _tabController;
  @override
  void initState() {
    super.initState();
    // Khởi tạo TabController với số lượng tabs bằng số lượng rooms
    // Đảm bảo có ít nhất 1 tab
    _tabController = TabController(
      length: widget.rooms.isNotEmpty ? widget.rooms.length : 1,
      vsync: this,
    );

  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<TagImageBloc, TagImageState>(
      listener: (context, state) {
        if (state.status == TagImageStatus.imageByComboTagSuccess) {
          final List<ImageByComboTag> data = state.data;
          images.setValue(data);
        }
      },
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Column(
            spacing: 12,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TabBar(
                controller: _tabController,
                isScrollable: true,
                tabs: widget.rooms.isNotEmpty
                    ? widget.rooms
                          .map(
                            (final room) => Tab(text: room.roomName ?? 'Room'),
                          )
                          .toList()
                    : [const Tab(text: 'No Rooms')],
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: widget.rooms.isNotEmpty
                      ? widget.rooms
                            .map(
                              (final room) => ValueListenableBuilder(
                                valueListenable: images,
                                builder: (context, vImage, child) {
                                  final imageByRoom = 
                                  vImage.where((final e)=> 
                                  e?.images.where((final ee)=> ee?.tags?.indexWhere((final eTag) =>
                                                                      eTag.itemGroupId == room.roomCode)
                                                                       != -1 )
                                                                      .toList()
                                                                      .isNotEmpty
                                                                      ?? false)
                                                                      .toList();
                                  
                                  return ValueListenableBuilder(
                                    valueListenable: widget.tags,
                                    builder: (context, vTags, child) {
                                      final tagByRoom = vTags
                                          .where(
                                            (final tagRoom) =>
                                                tagRoom.itemGroupId ==
                                                room.roomCode,
                                          )
                                          .toList();
                                   
                                      return Column(
                                        children: [
                                          SizedBox(
                                            child: Wrap(
                                              spacing: 8,
                                              children: [
                                                ...List.generate(tagByRoom.length, (
                                                  final i,
                                                ) {
                                                  final tag = tagByRoom[i];
                                                  return Padding(
                                                    padding: const EdgeInsets.only(
                                                      right: 8.0,
                                                    ),
                                                    child: SizedBox(
                                                      child: Row(
                                                        children: [
                                                          Text(tag.tagName ?? ''),
                                                          IconButton(
                                                            onPressed: () {},
                                                            icon: const Icon(
                                                              Icons.close,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  );
                                                }),
                                              ],
                                            ),
                                          ),
                                        
                                                 
                                                for (final image in imageByRoom)
                                                  GridView.count(
                                                    crossAxisCount: 3,
                                                    shrinkWrap: true,
                                                    physics:
                                                        const BouncingScrollPhysics(),
                                                    mainAxisSpacing: 4,
                                                    crossAxisSpacing: 4,
                                                    childAspectRatio: 0.9,
                                                    children:
                                                     image?.images.map((final imageItem) => ColoredBox(
                                                      color: Colors.amber,
                                                      child: Image.network(imageItem?.imageUrl??'',
                                                      fit: BoxFit.contain,
                                                      ),
                                                     )).toList()??[SizedBox()]
                                                    // [
                                                    //   Text(image?.createdDate??'')
                                                    // // ...List.generate(image?.images.length??0, (final i) {
                                                    // //   final imageb = image?.images[i];
                                                    // //   return Image.network(imageb?.imageUrl??'');
                                                    // // }),

                                                    // ],
                                                  )
                                        ],
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                            .toList()
                      : [const Center(child: Text('No rooms available'))],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
