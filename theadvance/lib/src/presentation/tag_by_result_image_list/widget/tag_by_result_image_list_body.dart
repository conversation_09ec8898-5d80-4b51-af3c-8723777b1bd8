import 'package:flutter/material.dart';

import '../../../core/params/image_by_combo_tag_params.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../../domain/entities/image_by_combo_tag.dart';
import '../../tag_image/bloc/tag_image_bloc.dart';
import '../../widgets/widgets.dart';

class TagByResultImageListBody extends StatefulWidget {
  const TagByResultImageListBody({
    super.key,
    required this.tags,
    required this.rooms,
    required this.tabController,
  });
  final ValueNotifierList<ComboTag> tags;
  final List<CustomerGetRoomListItem> rooms;
  final TabController tabController;
  @override
  State<TagByResultImageListBody> createState() =>
      _TagByResultImageListBodyState();
}

class _TagByResultImageListBodyState extends State<TagByResultImageListBody>
    with TickerProviderStateMixin {
  final ValueNotifierList<ImageByComboTag?> images = ValueNotifierList([]);
  final ValueNotifierList<ComboTag> comboTagSelected = ValueNotifierList([]);

  @override
  void initState() {
    super.initState();
    // Khởi tạo TabController với số lượng tabs bằng số lượng rooms
    // Đảm bảo có ít nhất 1 tab
    if (widget.tags.value.isNotEmpty) {
      final index = widget.rooms.indexWhere(
        (final room) => room.roomCode == widget.tags.value.first.itemGroupId,
      );
      if (index != -1) {
        widget.tabController.animateTo(index);
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<TagImageBloc, TagImageState>(
      listener: (final context, final state) {
        if (state.status == TagImageStatus.imageByComboTagSuccess) {
          final List<ImageByComboTag> data = state.data;
          images.setValue(data);
        }
      },
      builder: (final context, final state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TabBar(
                tabAlignment: TabAlignment.start,
                controller: widget.tabController,
                isScrollable: true,
                indicatorColor: Theme.of(context).primaryColor,
                indicatorWeight: 0,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(1),
                  border: Border(
                    bottom: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 1.5,
                    ),
                  ),
                ),
                indicatorSize: TabBarIndicatorSize.label,
                labelColor: Theme.of(context).primaryColor,
                unselectedLabelColor: Theme.of(
                  context,
                ).hintColor.withValues(alpha: .8),
                labelStyle: Theme.of(
                  context,
                ).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w500),
                unselectedLabelStyle: Theme.of(
                  context,
                ).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w500),
                dividerColor: Colors.transparent,
                tabs: widget.rooms.isNotEmpty
                    ? widget.rooms
                          .map(
                            (final room) =>
                                Tab(height: 32, text: room.roomName ?? 'Room'),
                          )
                          .toList()
                    : [const Tab(text: 'No Rooms')],
              ),
              Expanded(
                child: TabBarView(
                  controller: widget.tabController,
                  children: widget.rooms.isNotEmpty
                      ? widget.rooms
                            .map(
                              (final room) => ValueListenableBuilder(
                                valueListenable: images,
                                builder: (final context, final vImage, final child) {
                                  final imageByRoom = vImage
                                      .map((final e) {
                                        if (e?.images == null) {
                                          return null;
                                        }

                                        final filteredImages = e!.images.where((
                                          final image,
                                        ) {
                                          return image?.tags?.any(
                                                (final tag) =>
                                                    tag.itemGroupId ==
                                                    room.roomCode,
                                              ) ??
                                              false;
                                        }).toList();

                                        if (filteredImages.isNotEmpty) {
                                          return ImageByComboTag(
                                            createdDate: e.createdDate,
                                            images: filteredImages,
                                          );
                                        }
                                        return null;
                                      })
                                      .where((final e) => e != null)
                                      .toList();

                                  return ValueListenableBuilder(
                                    valueListenable: widget.tags,
                                    builder: (final context, final vTags, final child) {
                                      final tagByRoom = vTags
                                          .where(
                                            (final tagRoom) =>
                                                tagRoom.itemGroupId ==
                                                room.roomCode,
                                          )
                                          .toList();

                                      return Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          if (tagByRoom.isNotEmpty &&
                                              tagByRoom.first.tagId != null)
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 12,
                                                  ),
                                              child: Wrap(
                                                spacing: 8,
                                                runSpacing: 8,
                                                children: [
                                                  ...List.generate(
                                                    tagByRoom
                                                        .where(
                                                          (final e) =>
                                                              e.tagId != null,
                                                        )
                                                        .length,
                                                    (final i) {
                                                      final tag = tagByRoom
                                                          .where(
                                                            (final e) =>
                                                                e.tagId != null,
                                                          )
                                                          .toList()[i];
                                                      return GestureDetector(
                                                        onTap: () {
                                                          widget.tags.remove(
                                                            tag,
                                                          );
                                                          context.read<TagImageBloc>().add(
                                                            ImageByComboTagGet(
                                                              ImageByComboTagQueryParams(
                                                                itemGroupId: tag
                                                                    .itemGroupId,
                                                                tagIds: tagByRoom
                                                                    .where(
                                                                      (
                                                                        final e,
                                                                      ) =>
                                                                          e.tagId !=
                                                                          null,
                                                                    )
                                                                    .map(
                                                                      (
                                                                        final tag,
                                                                      ) => tag
                                                                          .tagId,
                                                                    )
                                                                    .join(','),
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                        child: DecoratedBox(
                                                          decoration: BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius.circular(
                                                                  16,
                                                                ),
                                                            border: Border.all(
                                                              color: Theme.of(
                                                                context,
                                                              ).primaryColor,
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets.symmetric(
                                                                  vertical: 4,
                                                                  horizontal: 8,
                                                                ),
                                                            child: Row(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              children: [
                                                                Text(
                                                                  tag.tagName ??
                                                                      '',
                                                                ),
                                                                Icon(
                                                                  Icons.close,
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                  if (tagByRoom.length > 1)
                                                    GestureDetector(
                                                      onTap: () {
                                                        for (final element
                                                            in tagByRoom) {
                                                          widget.tags.remove(
                                                            element,
                                                          );
                                                        }
                                                      },
                                                      child: DecoratedBox(
                                                        decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                16,
                                                              ),
                                                          border: Border.all(
                                                            color: const Color(
                                                              0xffDEE3ED,
                                                            ),
                                                          ),
                                                        ),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets.symmetric(
                                                                vertical: 4,
                                                                horizontal: 8,
                                                              ),
                                                          child: Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              Icon(
                                                                Icons.delete,
                                                              ),
                                                              Text(
                                                                'Xoa tat car',
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),

                                          for (final image in imageByRoom)
                                            MediaQuery.removePadding(
                                              context: context,
                                              removeTop: true,
                                              child: GridView.count(
                                                crossAxisCount: 3,
                                                shrinkWrap: true,
                                                physics:
                                                    const BouncingScrollPhysics(),
                                                mainAxisSpacing: 4,
                                                crossAxisSpacing: 4,
                                                children:
                                                    image?.images
                                                        .map(
                                                          (
                                                            final imageItem,
                                                          ) => Image.network(
                                                            imageItem
                                                                    ?.imageUrl ??
                                                                '',
                                                            fit: BoxFit.contain,
                                                          ),
                                                        )
                                                        .toList() ??
                                                    [const SizedBox()],
                                              ),
                                            ),
                                        ],
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                            .toList()
                      : [const Center(child: Text('No rooms available'))],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
