import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

import '../../../core/params/image_by_combo_tag_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/utils/mappers.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/models/combo_tag_model.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../../injector/injector.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../../tag_image/bloc/tag_image_bloc.dart';
import '../../widgets/value_notifier_list.dart';
import '../widget/tag_by_result_image_list_body.dart';

@RoutePage()
class TagByResultImageListPage extends StatefulWidget {
  const TagByResultImageListPage({
    super.key,
    required this.tags,
    required this.rooms,
  });
  final List<CustomerGetRoomListItem> rooms;
  final List<String> tags;

  @override
  State<TagByResultImageListPage> createState() =>
      _TagByResultImageListPageState();
}

class _TagByResultImageListPageState extends State<TagByResultImageListPage>
    with TickerProviderStateMixin {
  final ValueNotifierList<ComboTag> tagsInit = ValueNotifierList([]);
  String roomIdInit = '';
  late TabController tabController;
  @override
  void initState() {
    tabController = TabController(
      length: widget.rooms.isNotEmpty ? widget.rooms.length : 1,
      vsync: this,
    );
    tagsInit.setValue(
      getIt<Mapper>().convertList(
        widget.tags
            .map((final e) => ComboTagModel.fromJson(json.decode(e)))
            .toList(),
      ),
    );

    super.initState();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) {
        return getIt<TagImageBloc>()..add(
          ImageByComboTagGet(
            ImageByComboTagQueryParams(
              itemGroupId: tagsInit.value.firstOrNull?.itemGroupId,
              tagIds:
                  tagsInit.value.isNotEmpty &&
                      tagsInit.value.firstOrNull?.tagId != null
                  ? tagsInit.value.map((final tag) => tag.tagId).join(',')
                  : null,
            ),
          ),
        );
      },
      child: BaseLayout(
        body: TagByResultImageListBody(
          tabController: tabController,
          tags: tagsInit,
          rooms: widget.rooms,
        ),
        title: Text(
          'Danh sách ảnh',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        actions: [
          BlocBuilder<TagImageBloc, TagImageState>(
            builder: (final context, final state) {
              return IconButton(
                onPressed: () {
                  context.router
                      .push(
                        TagImageSearchRoute(
                          rooms: widget.rooms,
                          isFromTagImagePage: false,
                        ),
                      )
                      .then((final val) {
                        if (val != null && val is String) {
                          if (context.mounted) {
                            final ComboTag comboTag = getIt<Mapper>().convert(
                              ComboTagModel.fromJson(json.decode(val)),
                            );
                            final index = widget.rooms.indexWhere(
                              (final room) =>
                                  room.roomCode == comboTag.itemGroupId,
                            );
                            if (index != -1) {
                              tabController.animateTo(index);
                            }
                            tagsInit.addFirstList([comboTag]);
                            final List<ComboTag> tagByTabCurrent = tagsInit
                                .value
                                .where(
                                  (final tag) =>
                                      tag.itemGroupId == comboTag.itemGroupId,
                                )
                                .toList();
                            context.read<TagImageBloc>().add(
                              ImageByComboTagGet(
                                ImageByComboTagQueryParams(
                                  itemGroupId: comboTag.itemGroupId,
                                  tagIds: tagByTabCurrent
                                      .where((final e) => e.tagId != null)
                                      .map((final tag) => tag.tagId)
                                      .join(','),
                                ),
                              ),
                            );
                          }
                        }
                      });
                },
                icon: EZResources.image(ImageParams(name: AppIcons.icSearch)),
              );
            },
          ),
        ],
      ),
    );
  }
}
