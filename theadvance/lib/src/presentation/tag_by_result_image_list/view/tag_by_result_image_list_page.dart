import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

import '../../../core/params/image_by_combo_tag_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/utils/mappers.dart';
import '../../../data/models/combo_tag_model.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../../injector/injector.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../../tag_image/bloc/tag_image_bloc.dart';
import '../../widgets/value_notifier_list.dart';
import '../widget/tag_by_result_image_list_body.dart';

@RoutePage()
class TagByResultImageListPage extends StatefulWidget {
  const TagByResultImageListPage({
    super.key,
    required this.tags,
    required this.rooms,
  });
  final List<CustomerGetRoomListItem> rooms;
  final List<String> tags;

  @override
  State<TagByResultImageListPage> createState() 
  => _TagByResultImageListPageState();
}

class _TagByResultImageListPageState extends State<TagByResultImageListPage> {
  final ValueNotifierList<ComboTag> tagsInit = ValueNotifierList([]);
  String roomIdInit = '';
  @override
  void initState() {
    tagsInit.setValue(getIt<Mapper>().convertList(
          widget.tags
              .map((final e) => ComboTagModel.fromJson(json.decode(e)))
              .toList()));
        
    super.initState();
  }
  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) {
        return  getIt<TagImageBloc>()
            ..add(ImageByComboTagGet(ImageByComboTagQueryParams(
              itemGroupId: tagsInit.value.firstOrNull?.itemGroupId,
              tagIds:tagsInit.value.isEmpty ?null
              : tagsInit.value.map((final tag)=> tag.tagId).join(','),
            )));
      },
      child: BaseLayout(
        body: TagByResultImageListBody(tags: tagsInit, rooms: widget.rooms),
        title: Text(
          'Danh sách ảnh',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        actions: [
          IconButton(onPressed: (){
            context.router.push(
              TagImageSearchRoute(rooms: widget.rooms, isFromTagImagePage: false)).then((final val){
                if(val != null && val is String){
                  if(context.mounted){
                      
                      context.read<TagImageBloc>().add(ImageByComboTagGet(
                        ImageByComboTagQueryParams(tagIds: val)));
                  }
                
                }
              });
          },
          icon: EZResources.image(ImageParams(name: AppIcons.icSearch)),
          )
        ],
      ),
    );
  }
}
