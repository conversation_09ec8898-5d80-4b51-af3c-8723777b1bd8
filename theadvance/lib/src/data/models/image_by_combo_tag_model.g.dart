// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image_by_combo_tag_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ImageByComboTagModel _$ImageByComboTagModelFromJson(
        Map<String, dynamic> json) =>
    ImageByComboTagModel(
      createdDate: json['CreatedDate']?.toString(),
      images: (json['Images'] is List)
          ? (json['Images'] as List<dynamic>)
              .map((e) => e == null || e is! Map
                  ? null
                  : ImageByComboTagImagesModel.fromJson(
                      e as Map<String, dynamic>))
              .toList()
          : [],
    );

ImageByComboTagImagesModel _$ImageByComboTagImagesModelFromJson(
        Map<String, dynamic> json) =>
    ImageByComboTagImagesModel(
      id: json['Id']?.toString(),
      customerCode: json['CustomerCode']?.toString(),
      imageId: json['ImageId']?.toString(),
      imageUrl: json['ImageUrl']?.toString(),
      imageCreateDate: json['ImageCreateDate']?.toString(),
      isTagged: bool.tryParse(json['IsTagged'].toString()),
      tags: (json['Tags'] is List)
          ? (json['Tags'] as List<dynamic>?)
              ?.map((e) => ComboTagModel.fromJson(e as Map<String, dynamic>))
              .toList()
          : [],
    );
