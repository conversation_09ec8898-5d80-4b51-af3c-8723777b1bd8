import 'package:json_annotation/json_annotation.dart';

import 'combo_tag_model.dart';

part 'image_by_combo_tag_model.g.dart';

@JsonSerializable(createToJson: false)
class ImageByComboTagModel {
  ImageByComboTagModel({this.createdDate, required this.images});

  factory ImageByComboTagModel.fromJson(final Map<String, dynamic> json) =>
      _$ImageByComboTagModelFromJson(json);

  @Json<PERSON>ey(name: 'CreatedDate')
  final String? createdDate;

  @J<PERSON><PERSON>ey(name: 'Images')
  final List<ImageByComboTagImagesModel?> images;
}

@JsonSerializable(createToJson: false)
class ImageByComboTagImagesModel {
  ImageByComboTagImagesModel({
    this.id,
    this.customerCode,
    this.imageId,
    this.imageUrl,
    this.imageCreateDate,
    this.isTagged,
    required this.tags,
  });

  factory ImageByComboTagImagesModel.from<PERSON>son(
    final Map<String, dynamic> json,
  ) => _$ImageByComboTagImagesModelFromJson(json);

  @JsonKey(name: 'Id')
  final String? id;

  @JsonKey(name: 'CustomerCode')
  final String? customerCode;

  @JsonKey(name: 'ImageId')
  final String? imageId;

  @JsonKey(name: 'ImageUrl')
  final String? imageUrl;

  @JsonKey(name: 'ImageCreateDate')
  final String? imageCreateDate;

  @JsonKey(name: 'IsTagged')
  final bool? isTagged;

  @JsonKey(name: 'Tags')
  final List<ComboTagModel>? tags;
}
