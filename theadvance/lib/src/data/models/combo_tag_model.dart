import 'package:json_annotation/json_annotation.dart';

part 'combo_tag_model.g.dart';

@JsonSerializable(createToJson: false)
class ComboTagModel {
  ComboTagModel({
    this.tagId,
    this.tagName,
    this.tagNameUnicode,
    this.itemGroupId,
    this.itemGroupName,
  });

  factory ComboTagModel.fromJson(final Map<String, dynamic> json) =>
      _$ComboTagModelFromJson(json);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'TagID')
  final String? tagId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'TagName')
  final String? tagName;

  @<PERSON>son<PERSON><PERSON>(name: 'TagNameUnicode')
  final String? tagNameUnicode;

  @J<PERSON><PERSON><PERSON>(name: 'ItemGroupId')
  final String? itemGroupId;

  @<PERSON>son<PERSON>ey(name: 'ItemGroupName')
  final String? itemGroupName;
}
