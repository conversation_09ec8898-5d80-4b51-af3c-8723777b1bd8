// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'taking_care_customer_api_service.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element

class _TakingCareCustomerApiService implements TakingCareCustomerApiService {
  _TakingCareCustomerApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<HttpResponse<TakingCareCustomerResponseModel?>> getTakingCareCustomer(
    TakingCareCustomerRequestParams takingCareCustomerRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(takingCareCustomerRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<TakingCareCustomerResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/work/assign/employee/detail',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerFinishTaskResponseModel?>>
      finishTaskTakingCareCustomer(
    TakingCareCustomerFinishTaskRequestParams
        takingCareCustomerFinishTaskRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = takingCareCustomerFinishTaskRequestParams;
    final _options =
        _setStreamType<HttpResponse<TakingCareCustomerFinishTaskResponseModel>>(
            Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/app/erp/work/assign',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerFinishTaskResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerFinishTaskResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerUploadImagesResponseModel?>>
      uploadImagesTakingCareCustomer(
    dynamic takingCareCustomerUploadImagesRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = takingCareCustomerUploadImagesRequestParams;
    final _options = _setStreamType<
        HttpResponse<TakingCareCustomerUploadImagesResponseModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/work/images',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerUploadImagesResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerUploadImagesResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerRemoveImageResponseModel?>>
      removeImageTakingCareCustomer(
    TakingCareCustomerRemoveImageRequestParams
        takingCareCustomerRemoveImageRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = takingCareCustomerRemoveImageRequestParams;
    final _options = _setStreamType<
        HttpResponse<TakingCareCustomerRemoveImageResponseModel>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/work/images',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerRemoveImageResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerRemoveImageResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerGetSectionResponseModel?>>
      getSectionTakingCareCustomer(
    TakingCareCustomerGetSectionRequestParams
        takingCareCustomerGetSectionRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(takingCareCustomerGetSectionRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<TakingCareCustomerGetSectionResponseModel>>(
            Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/app/acc/services/get-services-info',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerGetSectionResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerGetSectionResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerCreateTreatmentDetailsResponseModel?>>
      createTreatmentDetailsTakingCareCustomer(
    TakingCareCustomerCommonUpdateParams
        takingCareCustomerCreateTreatmentDetailsRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data
        .addAll(takingCareCustomerCreateTreatmentDetailsRequestParams.toJson());
    final _options = _setStreamType<
        HttpResponse<
            TakingCareCustomerCreateTreatmentDetailsResponseModel>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/work/treatment/detail',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerCreateTreatmentDetailsResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerCreateTreatmentDetailsResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerCreateSupportResponseModel?>>
      createSupportTakingCareCustomer(
    TakingCareCustomerCreateSupportRequestParams
        takingCareCustomerCreateSupportRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = takingCareCustomerCreateSupportRequestParams;
    final _options = _setStreamType<
        HttpResponse<TakingCareCustomerCreateSupportResponseModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/advisory/support',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerCreateSupportResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerCreateSupportResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerCheckEmployeeInRoomResponseModel?>>
      checkEmployeeInRoomTakingCareCustomer(
    TakingCareCustomerCheckEmployeeInRoomRequestParams
        takingCareCustomerCheckEmployeeInRoomRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters
        .addAll(takingCareCustomerCheckEmployeeInRoomRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
            HttpResponse<TakingCareCustomerCheckEmployeeInRoomResponseModel>>(
        Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/app/erp/employee/check-staff',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerCheckEmployeeInRoomResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerCheckEmployeeInRoomResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerBotTypeLoadResponseModel?>>
      botTypeLoadTakingCareCustomer(
    TakingCareCustomerBotTypeLoadRequestParams
        takingCareCustomerBotTypeLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(takingCareCustomerBotTypeLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<TakingCareCustomerBotTypeLoadResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/config/bot-type',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerBotTypeLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerBotTypeLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerNotiBotTypePostResponseModel?>>
      notiBotTypeLoadTakingCareCustomer(
    TakingCareCustomerNotiBotTypePostRequestParams
        takingCareCustomerNotiBotTypeLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = takingCareCustomerNotiBotTypeLoadRequestParams;
    final _options = _setStreamType<
        HttpResponse<TakingCareCustomerNotiBotTypePostResponseModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/treatment/notify',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerNotiBotTypePostResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerNotiBotTypePostResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerGetTreatmentPhotoResponseModel?>>
      getTreatmentPhotoTakingCareCustomer(
    TakingCareCustomerGetTreatmentPhotoRequestParams
        takingCareCustomerGetTreatmentPhotoRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters
        .addAll(takingCareCustomerGetTreatmentPhotoRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<TakingCareCustomerGetTreatmentPhotoResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/app/erp/treatment/get-treatment-photo-by-customer-service-id',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerGetTreatmentPhotoResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerGetTreatmentPhotoResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<TakingCareCustomerFinishTaskResponseModel?>>
      updateServiceDetailRequestParams(
    dynamic params, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = params;
    final _options =
        _setStreamType<HttpResponse<TakingCareCustomerFinishTaskResponseModel>>(
            Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/app/acc/services-assign/update-service-detail',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late TakingCareCustomerFinishTaskResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : TakingCareCustomerFinishTaskResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
