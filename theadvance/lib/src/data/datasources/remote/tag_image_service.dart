import 'package:ez_core/ez_core.dart';

import '../../../core/network/end_points.dart';
import '../../../core/params/combo_tag_request_params.dart';
import '../../../core/params/image_by_combo_tag_params.dart';
import '../../models/combo_tag_model.dart';
import '../../models/image_by_combo_tag_model.dart';

part 'tag_image_service.g.dart';

@RestApi()
abstract class TagImageApiService {
  factory TagImageApiService(final Dio dio, {final String baseUrl}) =
      _TagImageApiService;

  @GET(EndPoints.getComboTag)
  Future<HttpResponse<GenericResponseModel<List<ComboTagModel>>?>> getComboTag(
    @Queries() final ComboTagRequestParams query, {
    @Header('isMockUp') final bool? isMockUp,
  });

  @GET(EndPoints.getTagImageByComboTag)
  Future<HttpResponse<GenericResponseModel<List<ImageByComboTagModel>>?>>
  getImageByComboTag(
    @Queries() final ImageByComboTagQueryParams query, {
    @Header('isMockUp') final bool? isMockUp,
  });
}
