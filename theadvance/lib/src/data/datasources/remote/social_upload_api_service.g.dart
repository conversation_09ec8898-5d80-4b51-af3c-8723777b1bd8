// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'social_upload_api_service.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element

class _SocialApiService implements SocialApiService {
  _SocialApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<HttpResponse<GenericResponseModel<SocialUploadFileModel>?>>
      uploadFileSocial(
    List<MultipartFile> files,
    String fileName, {
    void Function(int, int)? onSendProgress,
    CancelToken? onCancelRequest,
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = FormData();
    _data.files.addAll(files.map((i) => MapEntry('file', i)));
    _data.fields.add(MapEntry(
      'filename',
      fileName,
    ));
    final _options = _setStreamType<
        HttpResponse<GenericResponseModel<SocialUploadFileModel>>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
      contentType: 'multipart/form-data',
    )
        .compose(
          _dio.options,
          '/social/upload',
          queryParameters: queryParameters,
          data: _data,
          cancelToken: onCancelRequest,
          onSendProgress: onSendProgress,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GenericResponseModel<SocialUploadFileModel>? _value;
    try {
      _value = _result.data == null
          ? null
          : GenericResponseModel<SocialUploadFileModel>.fromJson(
              _result.data!,
              (json) =>
                  SocialUploadFileModel.fromJson(json as Map<String, dynamic>),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
