// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_api_service.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element

class _DetailCrmCustomerApiService implements DetailCrmCustomerApiService {
  _DetailCrmCustomerApiService(this._dio, {this.baseUrl, this.errorLogger});

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<HttpResponse<DetailCrmCustomerResponseModel?>> getDetailCrmCustomer(
    DetailCrmCustomerRequestParams detailCrmCustomerRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(detailCrmCustomerRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<DetailCrmCustomerResponseModel>>(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/info',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerAdviceFetchResponseModel?>>
  adviceFetchDetailCrmCustomer(
    DetailCrmCustomerAdviceFetchRequestParams
    detailCrmCustomerAdviceFetchRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(detailCrmCustomerAdviceFetchRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<DetailCrmCustomerAdviceFetchResponseModel>>(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/consultant',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerAdviceFetchResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerAdviceFetchResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerServiceFetchResponseModel?>>
  serviceFetchDetailCrmCustomer(
    DetailCrmCustomerServiceFetchRequestParams
    detailCrmCustomerServiceFetchRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(detailCrmCustomerServiceFetchRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<
          HttpResponse<DetailCrmCustomerServiceFetchResponseModel>
        >(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/service',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerServiceFetchResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerServiceFetchResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerCallLogFetchResponseModel?>>
  callLogFetchDetailCrmCustomer(
    DetailCrmCustomerCallLogFetchRequestParams
    detailCrmCustomerCallLogFetchRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(detailCrmCustomerCallLogFetchRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<
          HttpResponse<DetailCrmCustomerCallLogFetchResponseModel>
        >(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/call-log',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerCallLogFetchResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerCallLogFetchResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerMessageLogFetchResponseModel?>>
  messageLogFetchDetailCrmCustomer(
    DetailCrmCustomerMessageLogFetchRequestParams
    detailCrmCustomerMessageLogFetchRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(
      detailCrmCustomerMessageLogFetchRequestParams.toJson(),
    );
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<
          HttpResponse<DetailCrmCustomerMessageLogFetchResponseModel>
        >(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/message',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerMessageLogFetchResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerMessageLogFetchResponseModel.fromJson(
              _result.data!,
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerBookingLogFetchResponseModel?>>
  bookingLogFetchDetailCrmCustomer(
    DetailCrmCustomerBookingLogFetchRequestParams
    detailCrmCustomerBookingLogFetchRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(
      detailCrmCustomerBookingLogFetchRequestParams.toJson(),
    );
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<
          HttpResponse<DetailCrmCustomerBookingLogFetchResponseModel>
        >(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/booking',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerBookingLogFetchResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerBookingLogFetchResponseModel.fromJson(
              _result.data!,
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerAdviceTypeFetchResponseModel?>>
  adviceTypeFetchDetailCrmCustomer(
    DetailCrmCustomerAdviceTypeFetchRequestParams
    detailCrmCustomerAdviceTypeFetchRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(
      detailCrmCustomerAdviceTypeFetchRequestParams.toJson(),
    );
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<
          HttpResponse<DetailCrmCustomerAdviceTypeFetchResponseModel>
        >(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/advice-type-fetch-detail-crm-customer',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerAdviceTypeFetchResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerAdviceTypeFetchResponseModel.fromJson(
              _result.data!,
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerAdviceUpdateResponseModel?>>
  adviceUpdateDetailCrmCustomer(
    dynamic detailCrmCustomerAdviceUpdateRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = detailCrmCustomerAdviceUpdateRequestParams;
    final _options =
        _setStreamType<
          HttpResponse<DetailCrmCustomerAdviceUpdateResponseModel>
        >(
          Options(method: 'POST', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/consultant',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerAdviceUpdateResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerAdviceUpdateResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerBranchLoadResponseModel?>>
  branchLoadDetailCrmCustomer(
    DetailCrmCustomerBranchLoadRequestParams
    detailCrmCustomerBranchLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(detailCrmCustomerBranchLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<DetailCrmCustomerBranchLoadResponseModel>>(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/branch',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerBranchLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerBranchLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerRoomLoadResponseModel?>>
  roomLoadDetailCrmCustomer(
    DetailCrmCustomerRoomLoadRequestParams
    detailCrmCustomerRoomLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(detailCrmCustomerRoomLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<DetailCrmCustomerRoomLoadResponseModel>>(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/department',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerRoomLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerRoomLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerTimeLoadResponseModel?>>
  timeLoadDetailCrmCustomer(
    DetailCrmCustomerTimeLoadRequestParams
    detailCrmCustomerTimeLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(detailCrmCustomerTimeLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<DetailCrmCustomerTimeLoadResponseModel>>(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/booking-time',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerTimeLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerTimeLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerServiceLoadResponseModel?>>
  serviceLoadDetailCrmCustomer(
    DetailCrmCustomerServiceLoadRequestParams
    detailCrmCustomerServiceLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(detailCrmCustomerServiceLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<DetailCrmCustomerServiceLoadResponseModel>>(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/service',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerServiceLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerServiceLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerPromotionLoadResponseModel?>>
  promotionLoadDetailCrmCustomer(
    DetailCrmCustomerPromotionLoadRequestParams
    detailCrmCustomerPromotionLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(
      detailCrmCustomerPromotionLoadRequestParams.toJson(),
    );
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<
          HttpResponse<DetailCrmCustomerPromotionLoadResponseModel>
        >(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/promotion',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerPromotionLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerPromotionLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerBookingDetailLoadResponseModel?>>
  bookingDetailLoadDetailCrmCustomer(
    DetailCrmCustomerBookingDetailLoadRequestParams
    detailCrmCustomerBookingDetailLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(
      detailCrmCustomerBookingDetailLoadRequestParams.toJson(),
    );
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<
          HttpResponse<DetailCrmCustomerBookingDetailLoadResponseModel>
        >(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/booking/detail',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerBookingDetailLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerBookingDetailLoadResponseModel.fromJson(
              _result.data!,
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerNumberBookingLoadResponseModel?>>
  numberBookingLoadDetailCrmCustomer(
    DetailCrmCustomerNumberBookingLoadRequestParams
    detailCrmCustomerNumberBookingLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(
      detailCrmCustomerNumberBookingLoadRequestParams.toJson(),
    );
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<
          HttpResponse<DetailCrmCustomerNumberBookingLoadResponseModel>
        >(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/number-booking',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerNumberBookingLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerNumberBookingLoadResponseModel.fromJson(
              _result.data!,
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerBookResponseModel?>>
  bookDetailCrmCustomer(
    DetailCrmCustomerBookRequestParams detailCrmCustomerBookRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = detailCrmCustomerBookRequestParams;
    final _options =
        _setStreamType<HttpResponse<DetailCrmCustomerBookResponseModel>>(
          Options(method: 'POST', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/booking',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerBookResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerBookResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<DetailCrmCustomerBookingLoadResponseModel?>>
  bookingLoadDetailCrmCustomer(
    DetailCrmCustomerBookingLoadRequestParams
    detailCrmCustomerBookingLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(detailCrmCustomerBookingLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<DetailCrmCustomerBookingLoadResponseModel>>(
          Options(method: 'GET', headers: _headers, extra: _extra)
              .compose(
                _dio.options,
                '/app/crm/customer/booking/info',
                queryParameters: queryParameters,
                data: _data,
              )
              .copyWith(
                baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
              ),
        );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late DetailCrmCustomerBookingLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : DetailCrmCustomerBookingLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(String dioBaseUrl, String? baseUrl) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
